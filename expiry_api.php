<?php
require_once 'config.php';

// Güvenli session başlat
startSecureSession();

// JSON header'ı ayarla
header('Content-Type: application/json; charset=utf-8');
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');

// Kullanıcı oturum kontrolü
if (!isset($_SESSION['user_id']) || !isset($_SESSION['username'])) {
    sendJSONResponse([
        'success' => false,
        'message' => 'Oturum bulunamadı'
    ], 401);
}

try {
    // Kullanıcının aktif olup olmadığını kontrol et
    $stmt = $pdo->prepare("SELECT is_active FROM users WHERE id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $user = $stmt->fetch();
    
    if (!$user || !$user['is_active']) {
        sendJSONResponse([
            'success' => false,
            'message' => 'Kullanıcı aktif değil'
        ], 403);
    }
    
    // İstek metodunu kontrol et
    $method = $_SERVER['REQUEST_METHOD'];
    
    if ($method !== 'POST') {
        sendJSONResponse([
            'success' => false,
            'message' => 'Sadece POST istekleri kabul edilir'
        ], 405);
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    $action = $input['action'] ?? '';
    
    // Action'a göre işlem yap
    switch ($action) {
        case 'add_expiry':
            addExpiryItem($input);
            break;
            
        case 'get_expiry_items':
            getExpiryItems();
            break;
            
        case 'delete_expiry':
            deleteExpiryItem($input);
            break;
            
        case 'update_expiry':
            updateExpiryItem($input);
            break;
            
        default:
            sendJSONResponse([
                'success' => false,
                'message' => 'Geçersiz işlem'
            ], 400);
    }
    
} catch (PDOException $e) {
    logError('Expiry API database error: ' . $e->getMessage());
    sendJSONResponse([
        'success' => false,
        'message' => 'Veritabanı hatası: ' . $e->getMessage()
    ], 500);
} catch (Exception $e) {
    logError('Expiry API general error: ' . $e->getMessage());
    sendJSONResponse([
        'success' => false,
        'message' => 'Sistem hatası: ' . $e->getMessage()
    ], 500);
}

// Son kullanım tarihi öğesi ekle
function addExpiryItem($data) {
    global $pdo;
    
    $userId = $_SESSION['user_id'];
    $productCode = $data['product_code'] ?? '';
    $productName = $data['product_name'] ?? '';
    $expiryDate = $data['expiry_date'] ?? '';
    $quantity = $data['quantity'] ?? 1;
    
    if (empty($productCode) || empty($productName) || empty($expiryDate)) {
        sendJSONResponse([
            'success' => false,
            'message' => 'Tüm alanlar gereklidir'
        ], 400);
    }
    
    // Tarihi kontrol et
    $expiryDateTime = DateTime::createFromFormat('Y-m-d', $expiryDate);
    if (!$expiryDateTime) {
        sendJSONResponse([
            'success' => false,
            'message' => 'Geçersiz tarih formatı'
        ], 400);
    }
    
    // Aynı ürün ve tarih kombinasyonu var mı kontrol et
    $stmt = $pdo->prepare("
        SELECT id FROM expiry_tracking 
        WHERE user_id = ? AND product_code = ? AND expiry_date = ?
    ");
    $stmt->execute([$userId, $productCode, $expiryDate]);
    
    if ($stmt->fetch()) {
        sendJSONResponse([
            'success' => false,
            'message' => 'Bu ürün ve tarih kombinasyonu zaten mevcut'
        ], 400);
    }
    
    // Resim URL'si ve notları al
    $imageUrl = trim($data['image_url'] ?? '');
    $notes = trim($data['notes'] ?? '');

    // URL doğrulama (eğer girilmişse)
    if (!empty($imageUrl) && !filter_var($imageUrl, FILTER_VALIDATE_URL)) {
        sendJSONResponse([
            'success' => false,
            'message' => 'Geçersiz resim URL\'si'
        ], 400);
    }

    // Yeni kayıt ekle
    $stmt = $pdo->prepare("
        INSERT INTO expiry_tracking (user_id, product_code, product_name, expiry_date, quantity, product_image_url, notes)
        VALUES (?, ?, ?, ?, ?, ?, ?)
    ");
    $stmt->execute([$userId, $productCode, $productName, $expiryDate, $quantity, $imageUrl ?: null, $notes ?: null]);
    
    // Sistem loguna kaydet
    $stmt = $pdo->prepare("
        INSERT INTO system_logs (user_id, action, description, ip_address) 
        VALUES (?, 'expiry_added', ?, ?)
    ");
    $stmt->execute([
        $userId, 
        "Son kullanım tarihi eklendi: {$productName} ({$productCode})", 
        getClientIP()
    ]);
    
    sendJSONResponse([
        'success' => true,
        'message' => 'Ürün başarıyla eklendi'
    ]);
}

// Son kullanım tarihi öğelerini al
function getExpiryItems() {
    global $pdo;
    
    $userId = $_SESSION['user_id'];
    
    $stmt = $pdo->prepare("
        SELECT id, product_code, product_name, expiry_date, quantity, created_at
        FROM expiry_tracking 
        WHERE user_id = ?
        ORDER BY expiry_date ASC
    ");
    $stmt->execute([$userId]);
    $items = $stmt->fetchAll();
    
    sendJSONResponse([
        'success' => true,
        'items' => $items,
        'count' => count($items)
    ]);
}

// Son kullanım tarihi öğesini sil
function deleteExpiryItem($data) {
    global $pdo;
    
    $userId = $_SESSION['user_id'];
    $itemId = $data['id'] ?? '';
    
    if (empty($itemId)) {
        sendJSONResponse([
            'success' => false,
            'message' => 'Öğe ID gereklidir'
        ], 400);
    }
    
    // Önce öğenin kullanıcıya ait olup olmadığını kontrol et
    $stmt = $pdo->prepare("
        SELECT product_name, product_code FROM expiry_tracking 
        WHERE id = ? AND user_id = ?
    ");
    $stmt->execute([$itemId, $userId]);
    $item = $stmt->fetch();
    
    if (!$item) {
        sendJSONResponse([
            'success' => false,
            'message' => 'Öğe bulunamadı veya yetkiniz yok'
        ], 404);
    }
    
    // Öğeyi sil
    $stmt = $pdo->prepare("DELETE FROM expiry_tracking WHERE id = ? AND user_id = ?");
    $stmt->execute([$itemId, $userId]);
    
    // Sistem loguna kaydet
    $stmt = $pdo->prepare("
        INSERT INTO system_logs (user_id, action, description, ip_address) 
        VALUES (?, 'expiry_deleted', ?, ?)
    ");
    $stmt->execute([
        $userId, 
        "Son kullanım tarihi silindi: {$item['product_name']} ({$item['product_code']})", 
        getClientIP()
    ]);
    
    sendJSONResponse([
        'success' => true,
        'message' => 'Ürün başarıyla silindi'
    ]);
}

// Son kullanım tarihi öğesini güncelle
function updateExpiryItem($data) {
    global $pdo;
    
    $userId = $_SESSION['user_id'];
    $itemId = $data['id'] ?? '';
    $expiryDate = $data['expiry_date'] ?? '';
    $quantity = $data['quantity'] ?? 1;
    
    if (empty($itemId) || empty($expiryDate)) {
        sendJSONResponse([
            'success' => false,
            'message' => 'ID ve tarih gereklidir'
        ], 400);
    }
    
    // Tarihi kontrol et
    $expiryDateTime = DateTime::createFromFormat('Y-m-d', $expiryDate);
    if (!$expiryDateTime) {
        sendJSONResponse([
            'success' => false,
            'message' => 'Geçersiz tarih formatı'
        ], 400);
    }
    
    // Önce öğenin kullanıcıya ait olup olmadığını kontrol et
    $stmt = $pdo->prepare("
        SELECT product_name, product_code FROM expiry_tracking 
        WHERE id = ? AND user_id = ?
    ");
    $stmt->execute([$itemId, $userId]);
    $item = $stmt->fetch();
    
    if (!$item) {
        sendJSONResponse([
            'success' => false,
            'message' => 'Öğe bulunamadı veya yetkiniz yok'
        ], 404);
    }
    
    // Öğeyi güncelle
    $stmt = $pdo->prepare("
        UPDATE expiry_tracking 
        SET expiry_date = ?, quantity = ? 
        WHERE id = ? AND user_id = ?
    ");
    $stmt->execute([$expiryDate, $quantity, $itemId, $userId]);
    
    // Sistem loguna kaydet
    $stmt = $pdo->prepare("
        INSERT INTO system_logs (user_id, action, description, ip_address) 
        VALUES (?, 'expiry_updated', ?, ?)
    ");
    $stmt->execute([
        $userId, 
        "Son kullanım tarihi güncellendi: {$item['product_name']} ({$item['product_code']})", 
        getClientIP()
    ]);
    
    sendJSONResponse([
        'success' => true,
        'message' => 'Ürün başarıyla güncellendi'
    ]);
}
?>
