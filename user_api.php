<?php
require_once 'config.php';

// Güvenli session başlat
startSecureSession();

// JSON header'ı ayarla
header('Content-Type: application/json; charset=utf-8');
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');

// Kullanıcı oturum kontrolü
if (!isset($_SESSION['user_id']) || !isset($_SESSION['username'])) {
    sendJSONResponse([
        'success' => false,
        'message' => 'Oturum bulunamadı'
    ], 401);
}

try {
    // Kullanıcının aktif olup olmadığını kontrol et
    $stmt = $pdo->prepare("SELECT is_active FROM users WHERE id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $user = $stmt->fetch();
    
    if (!$user || !$user['is_active']) {
        sendJSONResponse([
            'success' => false,
            'message' => 'Kullanıcı aktif değil'
        ], 403);
    }
    
    // İstek metodunu kontrol et
    $method = $_SERVER['REQUEST_METHOD'];
    
    if ($method !== 'POST') {
        sendJSONResponse([
            'success' => false,
            'message' => 'Sadece POST istekleri kabul edilir'
        ], 405);
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    $action = $input['action'] ?? '';
    
    // Action'a göre işlem yap
    switch ($action) {
        case 'get_user_settings':
            getUserSettings();
            break;
            
        case 'update_user_settings':
            updateUserSettings($input);
            break;
            
        case 'change_password':
            changePassword($input);
            break;

        case 'get_profile':
            getUserProfile();
            break;

        case 'update_profile':
            updateUserProfile($input);
            break;

        case 'get_profile_stats':
            getProfileStats();
            break;

        case 'get_user_activity':
            getUserActivity();
            break;

        default:
            sendJSONResponse([
                'success' => false,
                'message' => 'Geçersiz işlem'
            ], 400);
    }
    
} catch (PDOException $e) {
    logError('User API database error: ' . $e->getMessage());
    sendJSONResponse([
        'success' => false,
        'message' => 'Veritabanı hatası'
    ], 500);
} catch (Exception $e) {
    logError('User API general error: ' . $e->getMessage());
    sendJSONResponse([
        'success' => false,
        'message' => 'Sistem hatası'
    ], 500);
}

// Kullanıcı ayarlarını al
function getUserSettings() {
    global $pdo;
    
    $userId = $_SESSION['user_id'];
    
    $stmt = $pdo->prepare("
        SELECT id, username, email, first_name, last_name, phone, role, created_at, last_login
        FROM users 
        WHERE id = ?
    ");
    $stmt->execute([$userId]);
    $user = $stmt->fetch();
    
    if (!$user) {
        sendJSONResponse([
            'success' => false,
            'message' => 'Kullanıcı bulunamadı'
        ], 404);
    }
    
    sendJSONResponse([
        'success' => true,
        'user' => $user
    ]);
}

// Kullanıcı ayarlarını güncelle
function updateUserSettings($data) {
    global $pdo;
    
    $userId = $_SESSION['user_id'];
    
    // Güncellenebilir alanlar
    $allowedFields = ['first_name', 'last_name', 'phone'];
    $updateFields = [];
    $params = [];
    
    foreach ($allowedFields as $field) {
        if (isset($data[$field])) {
            $updateFields[] = "{$field} = ?";
            $params[] = $data[$field];
        }
    }
    
    if (empty($updateFields)) {
        sendJSONResponse([
            'success' => false,
            'message' => 'Güncellenecek alan bulunamadı'
        ], 400);
    }
    
    $params[] = $userId;
    
    $sql = "UPDATE users SET " . implode(', ', $updateFields) . " WHERE id = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    
    // Sistem loguna kaydet
    $stmt = $pdo->prepare("
        INSERT INTO system_logs (user_id, action, description, ip_address) 
        VALUES (?, 'profile_updated', 'Kullanıcı profil bilgilerini güncelledi', ?)
    ");
    $stmt->execute([$userId, getClientIP()]);
    
    sendJSONResponse([
        'success' => true,
        'message' => 'Profil bilgileri güncellendi'
    ]);
}

// Şifre değiştir
function changePassword($data) {
    global $pdo;
    
    $userId = $_SESSION['user_id'];
    $currentPassword = $data['current_password'] ?? '';
    $newPassword = $data['new_password'] ?? '';
    
    if (empty($currentPassword) || empty($newPassword)) {
        sendJSONResponse([
            'success' => false,
            'message' => 'Mevcut şifre ve yeni şifre gereklidir'
        ], 400);
    }
    
    if (strlen($newPassword) < 6) {
        sendJSONResponse([
            'success' => false,
            'message' => 'Yeni şifre en az 6 karakter olmalıdır'
        ], 400);
    }
    
    // Mevcut şifreyi kontrol et
    $stmt = $pdo->prepare("SELECT password_hash FROM users WHERE id = ?");
    $stmt->execute([$userId]);
    $user = $stmt->fetch();
    
    if (!$user || !password_verify($currentPassword, $user['password_hash'])) {
        sendJSONResponse([
            'success' => false,
            'message' => 'Mevcut şifre hatalı'
        ], 400);
    }
    
    // Yeni şifreyi hashle ve güncelle
    $newPasswordHash = password_hash($newPassword, PASSWORD_DEFAULT);
    
    $stmt = $pdo->prepare("UPDATE users SET password_hash = ? WHERE id = ?");
    $stmt->execute([$newPasswordHash, $userId]);
    
    // Tüm remember token'larını sil (güvenlik için)
    $stmt = $pdo->prepare("DELETE FROM remember_tokens WHERE user_id = ?");
    $stmt->execute([$userId]);
    
    // Sistem loguna kaydet
    $stmt = $pdo->prepare("
        INSERT INTO system_logs (user_id, action, description, ip_address) 
        VALUES (?, 'password_changed', 'Kullanıcı şifresini değiştirdi', ?)
    ");
    $stmt->execute([$userId, getClientIP()]);
    
    sendJSONResponse([
        'success' => true,
        'message' => 'Şifre başarıyla değiştirildi'
    ]);
}

// Kullanıcı profil bilgilerini al
function getUserProfile() {
    global $pdo;

    $userId = $_SESSION['user_id'];

    $stmt = $pdo->prepare("
        SELECT id, username, email, first_name, last_name, phone, bio, role, created_at, last_login
        FROM users
        WHERE id = ?
    ");
    $stmt->execute([$userId]);
    $user = $stmt->fetch();

    if (!$user) {
        sendJSONResponse([
            'success' => false,
            'message' => 'Kullanıcı bulunamadı'
        ], 404);
    }

    sendJSONResponse([
        'success' => true,
        'user' => $user
    ]);
}

// Kullanıcı profilini güncelle
function updateUserProfile($data) {
    global $pdo;

    $userId = $_SESSION['user_id'];

    // Güncellenebilir alanlar
    $allowedFields = ['first_name', 'last_name', 'phone', 'bio'];
    $updateFields = [];
    $params = [];

    foreach ($allowedFields as $field) {
        if (isset($data[$field])) {
            $updateFields[] = "{$field} = ?";
            $params[] = $data[$field];
        }
    }

    if (empty($updateFields)) {
        sendJSONResponse([
            'success' => false,
            'message' => 'Güncellenecek alan bulunamadı'
        ], 400);
    }

    $params[] = $userId;

    $sql = "UPDATE users SET " . implode(', ', $updateFields) . " WHERE id = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);

    // Sistem loguna kaydet
    $stmt = $pdo->prepare("
        INSERT INTO system_logs (user_id, action, description, ip_address)
        VALUES (?, 'profile_updated', 'Kullanıcı profil bilgilerini güncelledi', ?)
    ");
    $stmt->execute([$userId, getClientIP()]);

    sendJSONResponse([
        'success' => true,
        'message' => 'Profil bilgileri güncellendi'
    ]);
}

// Profil istatistiklerini al
function getProfileStats() {
    global $pdo;

    $userId = $_SESSION['user_id'];

    // Favori sayısı
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM user_favorites WHERE user_id = ?");
    $stmt->execute([$userId]);
    $favoriteCount = $stmt->fetch()['count'];

    // Arama sayısı (sistem loglarından)
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM system_logs WHERE user_id = ? AND action = 'search'");
    $stmt->execute([$userId]);
    $searchCount = $stmt->fetch()['count'];

    sendJSONResponse([
        'success' => true,
        'stats' => [
            'favorite_count' => $favoriteCount,
            'search_count' => $searchCount
        ]
    ]);
}

// Kullanıcı aktivitelerini al
function getUserActivity() {
    global $pdo;

    $userId = $_SESSION['user_id'];

    $stmt = $pdo->prepare("
        SELECT action, description, ip_address, created_at
        FROM system_logs
        WHERE user_id = ?
        ORDER BY created_at DESC
        LIMIT 20
    ");
    $stmt->execute([$userId]);
    $activities = $stmt->fetchAll();

    sendJSONResponse([
        'success' => true,
        'activities' => $activities
    ]);
}
?>
