<?php
require_once 'config.php';

// Güvenli session başlat
startSecureSession();

// JSON header'ı ayarla
header('Content-Type: application/json; charset=utf-8');
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');

try {
    // Session kontrolü
    if (!isset($_SESSION['user_id']) || !isset($_SESSION['username'])) {
        // Remember me token kontrolü
        if (isset($_COOKIE['remember_token'])) {
            $token = $_COOKIE['remember_token'];
            $hashedToken = hash('sha256', $token);
            
            $stmt = $pdo->prepare("
                SELECT u.id, u.username, u.email, u.is_active, u.last_login
                FROM users u
                INNER JOIN remember_tokens rt ON u.id = rt.user_id
                WHERE rt.token = ? AND rt.expires_at > NOW() AND u.is_active = 1
            ");
            $stmt->execute([$hashedToken]);
            $user = $stmt->fetch();
            
            if ($user) {
                // Session'ı yeniden oluştur
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['email'] = $user['email'];
                $_SESSION['login_time'] = time();
                $_SESSION['last_activity'] = time();
                
                // Token'ı yenile
                $newToken = generateToken();
                $newExpiry = date('Y-m-d H:i:s', time() + (30 * 24 * 60 * 60));
                
                $stmt = $pdo->prepare("
                    UPDATE remember_tokens 
                    SET token = ?, expires_at = ? 
                    WHERE user_id = ?
                ");
                $stmt->execute([hash('sha256', $newToken), $newExpiry, $user['id']]);
                
                setcookie('remember_token', $newToken, time() + (30 * 24 * 60 * 60), '/', '', true, true);
                
                sendJSONResponse([
                    'authenticated' => true,
                    'user' => [
                        'id' => $user['id'],
                        'username' => $user['username'],
                        'email' => $user['email'],
                        'last_login' => $user['last_login']
                    ]
                ]);
            }
        }
        
        sendJSONResponse([
            'authenticated' => false,
            'message' => 'Oturum bulunamadı'
        ]);
    }
    
    // Session timeout kontrolü
    $sessionTimeout = SESSION_TIMEOUT; // config.php'den
    if (isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity']) > $sessionTimeout) {
        // Session süresi dolmuş
        session_destroy();
        
        sendJSONResponse([
            'authenticated' => false,
            'message' => 'Oturum süresi doldu'
        ]);
    }
    
    // Son aktivite zamanını güncelle
    $_SESSION['last_activity'] = time();
    
    // Kullanıcı bilgilerini veritabanından al
    $stmt = $pdo->prepare("
        SELECT id, username, email, first_name, last_name, last_login, is_active
        FROM users 
        WHERE id = ? AND is_active = 1
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $user = $stmt->fetch();
    
    if (!$user) {
        // Kullanıcı bulunamadı veya aktif değil
        session_destroy();
        
        sendJSONResponse([
            'authenticated' => false,
            'message' => 'Kullanıcı bulunamadı'
        ]);
    }
    
    // Session hijacking koruması
    $currentIP = getClientIP();
    if (isset($_SESSION['ip_address']) && $_SESSION['ip_address'] !== $currentIP) {
        // IP adresi değişmiş, güvenlik riski
        session_destroy();
        
        // Güvenlik logunu kaydet
        logError('Possible session hijacking detected', [
            'user_id' => $_SESSION['user_id'],
            'original_ip' => $_SESSION['ip_address'],
            'current_ip' => $currentIP
        ]);
        
        sendJSONResponse([
            'authenticated' => false,
            'message' => 'Güvenlik ihlali tespit edildi'
        ]);
    }
    
    // IP adresini session'a kaydet (ilk kez)
    if (!isset($_SESSION['ip_address'])) {
        $_SESSION['ip_address'] = $currentIP;
    }
    
    // Başarılı response
    sendJSONResponse([
        'authenticated' => true,
        'user' => [
            'id' => $user['id'],
            'username' => $user['username'],
            'email' => $user['email'],
            'first_name' => $user['first_name'],
            'last_name' => $user['last_name'],
            'last_login' => $user['last_login']
        ],
        'session_info' => [
            'login_time' => $_SESSION['login_time'] ?? time(),
            'last_activity' => $_SESSION['last_activity'],
            'expires_in' => $sessionTimeout - (time() - $_SESSION['last_activity'])
        ]
    ]);
    
} catch (PDOException $e) {
    logError('Session check database error: ' . $e->getMessage());
    
    sendJSONResponse([
        'authenticated' => false,
        'message' => 'Veritabanı hatası'
    ], 500);
    
} catch (Exception $e) {
    logError('Session check general error: ' . $e->getMessage());
    
    sendJSONResponse([
        'authenticated' => false,
        'message' => 'Sistem hatası'
    ], 500);
}
?>
