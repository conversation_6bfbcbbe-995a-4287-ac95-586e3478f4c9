<?php
require_once 'config.php';

// Güvenli session başlat
startSecureSession();

// JSON header'ı ayarla
header('Content-Type: application/json; charset=utf-8');
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');

// Kullanıcı oturum kontrolü
if (!isset($_SESSION['user_id']) || !isset($_SESSION['username'])) {
    sendJSONResponse([
        'success' => false,
        'message' => 'Oturum bulunamadı'
    ], 401);
}

try {
    // Kullanıcının aktif olup olmadığını kontrol et
    $stmt = $pdo->prepare("SELECT is_active FROM users WHERE id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $user = $stmt->fetch();
    
    if (!$user || !$user['is_active']) {
        sendJSONResponse([
            'success' => false,
            'message' => 'Kullanıcı aktif değil'
        ], 403);
    }
    
    // İstek metodunu kontrol et
    $method = $_SERVER['REQUEST_METHOD'];
    
    if ($method !== 'POST') {
        sendJSONResponse([
            'success' => false,
            'message' => 'Sadece POST istekleri kabul edilir'
        ], 405);
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    $action = $input['action'] ?? '';
    
    // Action'a göre işlem yap
    switch ($action) {
        case 'search':
            searchProducts($input);
            break;
            
        case 'get_product':
            getProduct($input);
            break;
            
        case 'toggle_favorite':
            toggleFavorite($input);
            break;
            
        case 'check_favorite':
            checkFavorite($input);
            break;
            
        case 'get_favorites':
            getFavorites();
            break;
            
        default:
            sendJSONResponse([
                'success' => false,
                'message' => 'Geçersiz işlem'
            ], 400);
    }
    
} catch (PDOException $e) {
    logError('Product API database error: ' . $e->getMessage());
    sendJSONResponse([
        'success' => false,
        'message' => 'Veritabanı hatası'
    ], 500);
} catch (Exception $e) {
    logError('Product API general error: ' . $e->getMessage());
    sendJSONResponse([
        'success' => false,
        'message' => 'Sistem hatası'
    ], 500);
}

// Türkçe karakter dönüştürme fonksiyonu
function turkishToEnglish($text) {
    $turkish = ['ç', 'ğ', 'ı', 'ö', 'ş', 'ü', 'Ç', 'Ğ', 'I', 'İ', 'Ö', 'Ş', 'Ü'];
    $english = ['c', 'g', 'i', 'o', 's', 'u', 'C', 'G', 'I', 'I', 'O', 'S', 'U'];
    return str_replace($turkish, $english, $text);
}

// Gelişmiş ürün arama
function searchProducts($data) {
    global $pdo;

    $term = trim($data['term'] ?? '');
    $type = $data['type'] ?? 'smart';
    $limit = $data['limit'] ?? 20;

    if (empty($term)) {
        sendJSONResponse([
            'success' => false,
            'message' => 'Arama terimi gereklidir'
        ], 400);
    }

    try {
        $allProducts = [];
        $searchQuery = $term;

        // 1. TAM EŞLEŞME - En yüksek öncelik
        $stmt = $pdo->prepare("
            SELECT DISTINCT p.*, b.code as barcode_value, 'exact' as match_type, 100 as relevance_score
            FROM product p
            LEFT JOIN barcode b ON p.code = b.product_code
            WHERE LOWER(TRIM(p.code)) = LOWER(TRIM(?))
               OR LOWER(TRIM(p.name)) = LOWER(TRIM(?))
               OR LOWER(TRIM(b.code)) = LOWER(TRIM(?))
            ORDER BY p.name ASC
        ");
        $stmt->execute([$searchQuery, $searchQuery, $searchQuery]);
        $exactMatches = $stmt->fetchAll();

        // 2. BAŞLANGIÇ EŞLEŞMESI
        $stmt = $pdo->prepare("
            SELECT DISTINCT p.*, b.code as barcode_value, 'starts_with' as match_type, 90 as relevance_score
            FROM product p
            LEFT JOIN barcode b ON p.code = b.product_code
            WHERE (LOWER(p.code) LIKE LOWER(?)
               OR LOWER(p.name) LIKE LOWER(?)
               OR LOWER(b.code) LIKE LOWER(?))
            AND NOT (LOWER(TRIM(p.code)) = LOWER(TRIM(?))
               OR LOWER(TRIM(p.name)) = LOWER(TRIM(?))
               OR LOWER(TRIM(b.code)) = LOWER(TRIM(?)))
            ORDER BY p.name ASC
        ");
        $startTerm = $searchQuery . '%';
        $stmt->execute([$startTerm, $startTerm, $startTerm, $searchQuery, $searchQuery, $searchQuery]);
        $startsWithMatches = $stmt->fetchAll();

        // 3. İÇERİK EŞLEŞMESI
        $stmt = $pdo->prepare("
            SELECT DISTINCT p.*, b.code as barcode_value, 'contains' as match_type, 80 as relevance_score
            FROM product p
            LEFT JOIN barcode b ON p.code = b.product_code
            WHERE (LOWER(p.code) LIKE LOWER(?)
               OR LOWER(p.name) LIKE LOWER(?)
               OR LOWER(b.code) LIKE LOWER(?))
            AND NOT (LOWER(TRIM(p.code)) = LOWER(TRIM(?))
               OR LOWER(TRIM(p.name)) = LOWER(TRIM(?))
               OR LOWER(TRIM(b.code)) = LOWER(TRIM(?)))
            AND NOT (LOWER(p.code) LIKE LOWER(?)
               OR LOWER(p.name) LIKE LOWER(?)
               OR LOWER(b.code) LIKE LOWER(?))
            ORDER BY p.name ASC
        ");
        $containsTerm = '%' . $searchQuery . '%';
        $startTerm = $searchQuery . '%';
        $stmt->execute([
            $containsTerm, $containsTerm, $containsTerm,
            $searchQuery, $searchQuery, $searchQuery,
            $startTerm, $startTerm, $startTerm
        ]);
        $containsMatches = $stmt->fetchAll();

        // 4. TÜRKÇE KARAKTER DUYARSIZ ARAMA
        $fuzzyQuery = turkishToEnglish($searchQuery);
        if ($fuzzyQuery !== $searchQuery) {
            $stmt = $pdo->prepare("
                SELECT DISTINCT p.*, b.code as barcode_value, 'fuzzy' as match_type, 70 as relevance_score
                FROM product p
                LEFT JOIN barcode b ON p.code = b.product_code
                WHERE (LOWER(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(p.name, 'ç', 'c'), 'ğ', 'g'), 'ı', 'i'), 'ö', 'o'), 'ş', 's'), 'ü', 'u'), 'Ç', 'C'), 'Ğ', 'G'), 'I', 'i'), 'Ö', 'O'), 'Ş', 'S'), 'Ü', 'U')) LIKE LOWER(?)
                   OR LOWER(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(p.code, 'ç', 'c'), 'ğ', 'g'), 'ı', 'i'), 'ö', 'o'), 'ş', 's'), 'ü', 'u'), 'Ç', 'C'), 'Ğ', 'G'), 'I', 'i'), 'Ö', 'O'), 'Ş', 'S'), 'Ü', 'U')) LIKE LOWER(?))
                AND p.code NOT IN (
                    SELECT DISTINCT p2.code FROM product p2
                    LEFT JOIN barcode b2 ON p2.code = b2.product_code
                    WHERE LOWER(TRIM(p2.code)) = LOWER(TRIM(?))
                       OR LOWER(TRIM(p2.name)) = LOWER(TRIM(?))
                       OR LOWER(TRIM(b2.code)) = LOWER(TRIM(?))
                       OR LOWER(p2.code) LIKE LOWER(?)
                       OR LOWER(p2.name) LIKE LOWER(?)
                       OR LOWER(b2.code) LIKE LOWER(?)
                       OR LOWER(p2.code) LIKE LOWER(?)
                       OR LOWER(p2.name) LIKE LOWER(?)
                       OR LOWER(b2.code) LIKE LOWER(?)
                )
                ORDER BY p.name ASC
            ");
            $fuzzyTerm = '%' . $fuzzyQuery . '%';
            $containsTerm = '%' . $searchQuery . '%';
            $startTerm = $searchQuery . '%';
            $stmt->execute([
                $fuzzyTerm, $fuzzyTerm,
                $searchQuery, $searchQuery, $searchQuery,
                $startTerm, $startTerm, $startTerm,
                $containsTerm, $containsTerm, $containsTerm
            ]);
            $fuzzyMatches = $stmt->fetchAll();
        } else {
            $fuzzyMatches = [];
        }

        // 5. KELİME BAZLI ARAMA
        $words = array_filter(explode(' ', $searchQuery), function($word) {
            return strlen(trim($word)) > 2;
        });

        $wordMatches = [];
        if (count($words) > 1) {
            $wordConditions = [];
            $wordParams = [];
            foreach ($words as $word) {
                $wordConditions[] = "(LOWER(p.name) LIKE LOWER(?) OR LOWER(p.code) LIKE LOWER(?))";
                $wordTerm = '%' . trim($word) . '%';
                $wordParams[] = $wordTerm;
                $wordParams[] = $wordTerm;
            }

            if (!empty($wordConditions)) {
                $excludeSubquery = "
                    SELECT DISTINCT p2.code FROM product p2
                    LEFT JOIN barcode b2 ON p2.code = b2.product_code
                    WHERE LOWER(TRIM(p2.code)) = LOWER(TRIM(?))
                       OR LOWER(TRIM(p2.name)) = LOWER(TRIM(?))
                       OR LOWER(TRIM(b2.code)) = LOWER(TRIM(?))
                       OR LOWER(p2.code) LIKE LOWER(?)
                       OR LOWER(p2.name) LIKE LOWER(?)
                       OR LOWER(b2.code) LIKE LOWER(?)
                       OR LOWER(p2.code) LIKE LOWER(?)
                       OR LOWER(p2.name) LIKE LOWER(?)
                       OR LOWER(b2.code) LIKE LOWER(?)
                ";

                $wordQuery = "
                    SELECT DISTINCT p.*, b.code as barcode_value, 'word_match' as match_type, 60 as relevance_score
                    FROM product p
                    LEFT JOIN barcode b ON p.code = b.product_code
                    WHERE (" . implode(' AND ', $wordConditions) . ")
                    AND p.code NOT IN ($excludeSubquery)
                    ORDER BY p.name ASC
                ";

                $stmt = $pdo->prepare($wordQuery);
                $allParams = array_merge($wordParams, [
                    $searchQuery, $searchQuery, $searchQuery,
                    $searchQuery . '%', $searchQuery . '%', $searchQuery . '%',
                    '%' . $searchQuery . '%', '%' . $searchQuery . '%', '%' . $searchQuery . '%'
                ]);
                $stmt->execute($allParams);
                $wordMatches = $stmt->fetchAll();
            }
        }

        // Sonuçları birleştir
        $allMatches = array_merge($exactMatches, $startsWithMatches, $containsMatches, $fuzzyMatches, $wordMatches);

        // Duplicate'leri kaldır
        $uniqueProducts = [];
        $seenCodes = [];
        foreach ($allMatches as $product) {
            if (!in_array($product['code'], $seenCodes)) {
                // Her ürün için barkodları da al
                $stmt = $pdo->prepare("SELECT code FROM barcode WHERE product_code = ?");
                $stmt->execute([$product['code']]);
                $product['barcodes'] = $stmt->fetchAll(PDO::FETCH_COLUMN);

                $uniqueProducts[] = $product;
                $seenCodes[] = $product['code'];
            }
        }

        // İlk $limit kadar al
        $products = array_slice($uniqueProducts, 0, $limit);

        sendJSONResponse([
            'success' => true,
            'products' => $products,
            'count' => count($products),
            'search_info' => [
                'query' => $searchQuery,
                'exact_matches' => count($exactMatches),
                'starts_with_matches' => count($startsWithMatches),
                'contains_matches' => count($containsMatches),
                'fuzzy_matches' => count($fuzzyMatches),
                'word_matches' => count($wordMatches),
                'total_found' => count($uniqueProducts)
            ]
        ]);

    } catch (PDOException $e) {
        logError('Advanced product search error: ' . $e->getMessage());
        sendJSONResponse([
            'success' => false,
            'message' => 'Arama sırasında hata oluştu'
        ], 500);
    }
}

// Ürün detayını al
function getProduct($data) {
    global $pdo;
    
    $code = $data['code'] ?? '';
    
    if (empty($code)) {
        sendJSONResponse([
            'success' => false,
            'message' => 'Ürün kodu gereklidir'
        ], 400);
    }
    
    $stmt = $pdo->prepare("SELECT * FROM product WHERE code = ?");
    $stmt->execute([$code]);
    $product = $stmt->fetch();
    
    if (!$product) {
        sendJSONResponse([
            'success' => false,
            'message' => 'Ürün bulunamadı'
        ], 404);
    }
    
    // Ürünün barkodlarını da al
    $stmt = $pdo->prepare("SELECT code FROM barcode WHERE product_code = ?");
    $stmt->execute([$code]);
    $barcodes = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $product['barcodes'] = $barcodes;
    
    sendJSONResponse([
        'success' => true,
        'product' => $product
    ]);
}

// Favori durumunu değiştir
function toggleFavorite($data) {
    global $pdo;
    
    $productCode = $data['product_code'] ?? '';
    $userId = $_SESSION['user_id'];
    
    if (empty($productCode)) {
        sendJSONResponse([
            'success' => false,
            'message' => 'Ürün kodu gereklidir'
        ], 400);
    }
    
    // Ürünün var olup olmadığını kontrol et
    $stmt = $pdo->prepare("SELECT code FROM product WHERE code = ?");
    $stmt->execute([$productCode]);
    if (!$stmt->fetch()) {
        sendJSONResponse([
            'success' => false,
            'message' => 'Ürün bulunamadı'
        ], 404);
    }
    
    // Favori durumunu kontrol et
    $stmt = $pdo->prepare("SELECT id FROM user_favorites WHERE user_id = ? AND product_code = ?");
    $stmt->execute([$userId, $productCode]);
    $favorite = $stmt->fetch();
    
    if ($favorite) {
        // Favorilerden çıkar
        $stmt = $pdo->prepare("DELETE FROM user_favorites WHERE user_id = ? AND product_code = ?");
        $stmt->execute([$userId, $productCode]);
        
        sendJSONResponse([
            'success' => true,
            'is_favorite' => false,
            'message' => 'Ürün favorilerden çıkarıldı'
        ]);
    } else {
        // Favorilere ekle
        $stmt = $pdo->prepare("INSERT INTO user_favorites (user_id, product_code) VALUES (?, ?)");
        $stmt->execute([$userId, $productCode]);
        
        sendJSONResponse([
            'success' => true,
            'is_favorite' => true,
            'message' => 'Ürün favorilere eklendi'
        ]);
    }
}

// Favori durumunu kontrol et
function checkFavorite($data) {
    global $pdo;
    
    $productCode = $data['product_code'] ?? '';
    $userId = $_SESSION['user_id'];
    
    if (empty($productCode)) {
        sendJSONResponse([
            'success' => false,
            'message' => 'Ürün kodu gereklidir'
        ], 400);
    }
    
    $stmt = $pdo->prepare("SELECT id FROM user_favorites WHERE user_id = ? AND product_code = ?");
    $stmt->execute([$userId, $productCode]);
    $favorite = $stmt->fetch();
    
    sendJSONResponse([
        'success' => true,
        'is_favorite' => (bool)$favorite
    ]);
}

// Kullanıcının favori ürünlerini al
function getFavorites() {
    global $pdo;
    
    $userId = $_SESSION['user_id'];
    
    $stmt = $pdo->prepare("
        SELECT p.*, uf.created_at as favorite_date
        FROM user_favorites uf
        INNER JOIN product p ON uf.product_code = p.code
        WHERE uf.user_id = ?
        ORDER BY uf.created_at DESC
    ");
    $stmt->execute([$userId]);
    $favorites = $stmt->fetchAll();
    
    sendJSONResponse([
        'success' => true,
        'favorites' => $favorites,
        'count' => count($favorites)
    ]);
}
?>
