<?php
require_once 'config.php';

// Güvenli session başlat
startSecureSession();

// Sadece POST isteklerini kabul et
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendJSONResponse([
        'success' => false,
        'message' => 'Geçersiz istek metodu!'
    ], 405);
}

try {
    $userId = $_SESSION['user_id'] ?? null;
    $username = $_SESSION['username'] ?? null;
    $clientIP = getClientIP();
    
    // Remember me token'ını temizle
    if (isset($_COOKIE['remember_token'])) {
        $token = $_COOKIE['remember_token'];
        $hashedToken = hash('sha256', $token);
        
        // Veritabanından token'ı sil
        if ($userId) {
            $stmt = $pdo->prepare("DELETE FROM remember_tokens WHERE user_id = ? AND token = ?");
            $stmt->execute([$userId, $hashedToken]);
        }
        
        // Cookie'yi sil
        setcookie('remember_token', '', time() - 3600, '/', '', true, true);
    }
    
    // Tüm remember token'larını temizle (güvenlik için)
    if ($userId) {
        $stmt = $pdo->prepare("DELETE FROM remember_tokens WHERE user_id = ?");
        $stmt->execute([$userId]);
    }
    
    // Veritabanı tabanlı session'ları temizle (eğer kullanılıyorsa)
    if ($userId) {
        $stmt = $pdo->prepare("DELETE FROM user_sessions WHERE user_id = ?");
        $stmt->execute([$userId]);
    }
    
    // Logout işlemini logla
    if ($userId && $username) {
        // Sistem loguna kaydet
        $stmt = $pdo->prepare("
            INSERT INTO system_logs (user_id, action, description, ip_address, user_agent) 
            VALUES (?, 'logout', 'User logged out successfully', ?, ?)
        ");
        $stmt->execute([
            $userId, 
            $clientIP, 
            $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown'
        ]);
        
        // Login attempts tablosuna başarılı çıkış kaydı
        logLoginAttempt($clientIP, $username, true, 'logout');
    }
    
    // Session'ı tamamen yok et
    $_SESSION = array();
    
    // Session cookie'sini sil
    if (ini_get("session.use_cookies")) {
        $params = session_get_cookie_params();
        setcookie(session_name(), '', time() - 42000,
            $params["path"], $params["domain"],
            $params["secure"], $params["httponly"]
        );
    }
    
    // Session'ı yok et
    session_destroy();
    
    // Başarılı response
    sendJSONResponse([
        'success' => true,
        'message' => 'Başarıyla çıkış yaptınız'
    ]);
    
} catch (PDOException $e) {
    logError('Logout database error: ' . $e->getMessage(), [
        'user_id' => $userId,
        'ip' => $clientIP
    ]);
    
    // Hata durumunda da session'ı temizle
    session_destroy();
    
    sendJSONResponse([
        'success' => true, // Kullanıcı için başarılı göster
        'message' => 'Çıkış yapıldı'
    ]);
    
} catch (Exception $e) {
    logError('Logout general error: ' . $e->getMessage(), [
        'user_id' => $userId,
        'ip' => $clientIP
    ]);
    
    // Hata durumunda da session'ı temizle
    session_destroy();
    
    sendJSONResponse([
        'success' => true, // Kullanıcı için başarılı göster
        'message' => 'Çıkış yapıldı'
    ]);
}
?>
