<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Panel - Profesyonel Portal</title>
    <link rel="stylesheet" href="admin.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="admin-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-shield-alt"></i>
                    <span>Admin Panel</span>
                </div>
            </div>
            
            <nav class="sidebar-nav">
                <ul>
                    <li class="nav-item active">
                        <a href="#" class="nav-link" data-section="dashboard">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" data-section="users">
                            <i class="fas fa-users"></i>
                            <span>Kullanıcı Yönetimi</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" data-section="logs">
                            <i class="fas fa-list-alt"></i>
                            <span>Sistem Logları</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" data-section="security">
                            <i class="fas fa-shield-alt"></i>
                            <span>Güvenlik</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" data-section="settings">
                            <i class="fas fa-cog"></i>
                            <span>Sistem Ayarları</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="dashboard.html" class="nav-link">
                            <i class="fas fa-arrow-left"></i>
                            <span>Ana Panele Dön</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="header">
                <div class="header-left">
                    <button class="sidebar-toggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 id="pageTitle">Admin Dashboard</h1>
                </div>
                
                <div class="header-right">
                    <div class="user-menu">
                        <button class="user-btn" onclick="toggleUserMenu()">
                            <img src="https://via.placeholder.com/40x40/4f46e5/ffffff?text=A" alt="Admin" class="user-avatar">
                            <span class="user-name" id="adminName">Admin</span>
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        
                        <div class="user-dropdown" id="userDropdown">
                            <a href="#" class="dropdown-item">
                                <i class="fas fa-user"></i>
                                Profil
                            </a>
                            <div class="dropdown-divider"></div>
                            <a href="#" class="dropdown-item" onclick="logout()">
                                <i class="fas fa-sign-out-alt"></i>
                                Çıkış Yap
                            </a>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Dashboard Section -->
            <div id="dashboard-section" class="content-section active">
                <div class="admin-content">
                    <!-- Stats Cards -->
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-content">
                                <h3 id="totalUsers">-</h3>
                                <p>Toplam Kullanıcı</p>
                                <span class="stat-change positive">+12%</span>
                            </div>
                        </div>
                        
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-user-check"></i>
                            </div>
                            <div class="stat-content">
                                <h3 id="activeUsers">-</h3>
                                <p>Aktif Kullanıcı</p>
                                <span class="stat-change positive">+8%</span>
                            </div>
                        </div>
                        
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-sign-in-alt"></i>
                            </div>
                            <div class="stat-content">
                                <h3 id="todayLogins">-</h3>
                                <p>Bugünkü Girişler</p>
                                <span class="stat-change positive">+15%</span>
                            </div>
                        </div>
                        
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="stat-content">
                                <h3 id="failedAttempts">-</h3>
                                <p>Başarısız Denemeler</p>
                                <span class="stat-change negative">-5%</span>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="quick-actions">
                        <h3>Hızlı İşlemler</h3>
                        <div class="action-buttons">
                            <button class="action-btn" onclick="showSection('users')">
                                <i class="fas fa-user-plus"></i>
                                Yeni Kullanıcı
                            </button>
                            <button class="action-btn" onclick="showSection('logs')">
                                <i class="fas fa-eye"></i>
                                Logları Görüntüle
                            </button>
                            <button class="action-btn" onclick="showSection('security')">
                                <i class="fas fa-shield-alt"></i>
                                Güvenlik Raporu
                            </button>
                            <button class="action-btn" onclick="exportData()">
                                <i class="fas fa-download"></i>
                                Veri Dışa Aktar
                            </button>
                        </div>
                    </div>

                    <!-- Recent Activity -->
                    <div class="recent-activity">
                        <h3>Son Aktiviteler</h3>
                        <div class="activity-list" id="recentActivity">
                            <!-- JavaScript ile doldurulacak -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Users Section -->
            <div id="users-section" class="content-section">
                <div class="admin-content">
                    <div class="section-header">
                        <h2>Kullanıcı Yönetimi</h2>
                        <button class="btn-primary" onclick="showAddUserModal()">
                            <i class="fas fa-plus"></i>
                            Yeni Kullanıcı Ekle
                        </button>
                    </div>

                    <!-- User Filters -->
                    <div class="filters">
                        <div class="filter-group">
                            <input type="text" id="userSearch" placeholder="Kullanıcı ara..." class="search-input">
                            <select id="roleFilter" class="filter-select">
                                <option value="">Tüm Roller</option>
                                <option value="admin">Admin</option>
                                <option value="user">Kullanıcı</option>
                                <option value="moderator">Moderatör</option>
                            </select>
                            <select id="statusFilter" class="filter-select">
                                <option value="">Tüm Durumlar</option>
                                <option value="1">Aktif</option>
                                <option value="0">Pasif</option>
                            </select>
                        </div>
                    </div>

                    <!-- Users Table -->
                    <div class="table-container">
                        <table class="data-table" id="usersTable">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Kullanıcı Adı</th>
                                    <th>E-posta</th>
                                    <th>Ad Soyad</th>
                                    <th>Rol</th>
                                    <th>Durum</th>
                                    <th>Son Giriş</th>
                                    <th>İşlemler</th>
                                </tr>
                            </thead>
                            <tbody id="usersTableBody">
                                <!-- JavaScript ile doldurulacak -->
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="pagination" id="usersPagination">
                        <!-- JavaScript ile doldurulacak -->
                    </div>
                </div>
            </div>

            <!-- Logs Section -->
            <div id="logs-section" class="content-section">
                <div class="admin-content">
                    <div class="section-header">
                        <h2>Sistem Logları</h2>
                        <button class="btn-secondary" onclick="clearOldLogs()">
                            <i class="fas fa-trash"></i>
                            Eski Logları Temizle
                        </button>
                    </div>

                    <!-- Log Filters -->
                    <div class="filters">
                        <div class="filter-group">
                            <input type="date" id="logDateFrom" class="filter-input">
                            <input type="date" id="logDateTo" class="filter-input">
                            <select id="logActionFilter" class="filter-select">
                                <option value="">Tüm İşlemler</option>
                                <option value="login">Giriş</option>
                                <option value="logout">Çıkış</option>
                                <option value="user_created">Kullanıcı Oluşturma</option>
                                <option value="user_updated">Kullanıcı Güncelleme</option>
                                <option value="user_deleted">Kullanıcı Silme</option>
                            </select>
                            <button class="btn-primary" onclick="filterLogs()">Filtrele</button>
                        </div>
                    </div>

                    <!-- Logs Table -->
                    <div class="table-container">
                        <table class="data-table" id="logsTable">
                            <thead>
                                <tr>
                                    <th>Tarih</th>
                                    <th>Kullanıcı</th>
                                    <th>İşlem</th>
                                    <th>Açıklama</th>
                                    <th>IP Adresi</th>
                                </tr>
                            </thead>
                            <tbody id="logsTableBody">
                                <!-- JavaScript ile doldurulacak -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Security Section -->
            <div id="security-section" class="content-section">
                <div class="admin-content">
                    <div class="section-header">
                        <h2>Güvenlik Raporu</h2>
                        <button class="btn-primary" onclick="refreshSecurityData()">
                            <i class="fas fa-sync"></i>
                            Yenile
                        </button>
                    </div>

                    <!-- Security Stats -->
                    <div class="security-grid">
                        <div class="security-card">
                            <h4>Başarısız Giriş Denemeleri</h4>
                            <div class="security-number" id="securityFailedLogins">-</div>
                            <p>Son 24 saat</p>
                        </div>
                        <div class="security-card">
                            <h4>Kilitli Hesaplar</h4>
                            <div class="security-number" id="securityLockedAccounts">-</div>
                            <p>Şu anda kilitli</p>
                        </div>
                        <div class="security-card">
                            <h4>Şüpheli IP'ler</h4>
                            <div class="security-number" id="securitySuspiciousIPs">-</div>
                            <p>Son 7 gün</p>
                        </div>
                    </div>

                    <!-- Failed Attempts Table -->
                    <div class="table-container">
                        <h3>Son Başarısız Denemeler</h3>
                        <table class="data-table" id="failedAttemptsTable">
                            <thead>
                                <tr>
                                    <th>Tarih</th>
                                    <th>IP Adresi</th>
                                    <th>Kullanıcı Adı</th>
                                    <th>Deneme Sayısı</th>
                                    <th>İşlemler</th>
                                </tr>
                            </thead>
                            <tbody id="failedAttemptsTableBody">
                                <!-- JavaScript ile doldurulacak -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Settings Section -->
            <div id="settings-section" class="content-section">
                <div class="admin-content">
                    <div class="section-header">
                        <h2>Sistem Ayarları</h2>
                        <button class="btn-primary" onclick="saveSettings()">
                            <i class="fas fa-save"></i>
                            Ayarları Kaydet
                        </button>
                    </div>

                    <div class="settings-grid">
                        <div class="settings-card">
                            <h3>Güvenlik Ayarları</h3>
                            <div class="setting-item">
                                <label>Maksimum Giriş Denemesi</label>
                                <input type="number" id="maxLoginAttempts" value="5" min="1" max="10">
                            </div>
                            <div class="setting-item">
                                <label>Hesap Kilitleme Süresi (dakika)</label>
                                <input type="number" id="lockoutTime" value="15" min="5" max="60">
                            </div>
                            <div class="setting-item">
                                <label>Session Süresi (dakika)</label>
                                <input type="number" id="sessionTimeout" value="60" min="15" max="480">
                            </div>
                        </div>

                        <div class="settings-card">
                            <h3>Sistem Ayarları</h3>
                            <div class="setting-item">
                                <label>Otomatik Log Temizleme</label>
                                <select id="autoLogCleanup">
                                    <option value="7">7 Gün</option>
                                    <option value="30">30 Gün</option>
                                    <option value="90" selected>90 Gün</option>
                                    <option value="365">1 Yıl</option>
                                </select>
                            </div>
                            <div class="setting-item">
                                <label>E-posta Bildirimleri</label>
                                <input type="checkbox" id="emailNotifications" checked>
                            </div>
                            <div class="setting-item">
                                <label>Bakım Modu</label>
                                <input type="checkbox" id="maintenanceMode">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Add User Modal -->
    <div id="addUserModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Yeni Kullanıcı Ekle</h3>
                <button class="modal-close" onclick="closeAddUserModal()">&times;</button>
            </div>
            <form id="addUserForm" class="modal-body">
                <div class="form-row">
                    <div class="form-group">
                        <label for="newUsername">Kullanıcı Adı *</label>
                        <input type="text" id="newUsername" required>
                    </div>
                    <div class="form-group">
                        <label for="newEmail">E-posta *</label>
                        <input type="email" id="newEmail" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="newFirstName">Ad</label>
                        <input type="text" id="newFirstName">
                    </div>
                    <div class="form-group">
                        <label for="newLastName">Soyad</label>
                        <input type="text" id="newLastName">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="newPassword">Şifre *</label>
                        <input type="password" id="newPassword" required>
                    </div>
                    <div class="form-group">
                        <label for="newRole">Rol *</label>
                        <select id="newRole" required>
                            <option value="user">Kullanıcı</option>
                            <option value="moderator">Moderatör</option>
                            <option value="admin">Admin</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label for="newPhone">Telefon</label>
                    <input type="tel" id="newPhone">
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="newIsActive" checked>
                        Hesap Aktif
                    </label>
                </div>
            </form>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="closeAddUserModal()">İptal</button>
                <button type="submit" form="addUserForm" class="btn-primary">Kullanıcı Ekle</button>
            </div>
        </div>
    </div>

    <!-- Alert Container -->
    <div id="alertContainer"></div>

    <script src="admin.js"></script>
</body>
</html>
