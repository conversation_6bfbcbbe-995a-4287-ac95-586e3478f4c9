<?php
require_once 'config.php';

// JSON header'ı ayarla
header('Content-Type: application/json; charset=utf-8');
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');

// CORS headers
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// OPTIONS request için
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    // İstek metodunu kontrol et
    $method = $_SERVER['REQUEST_METHOD'];
    
    if ($method !== 'POST') {
        sendJSONResponse([
            'success' => false,
            'message' => 'Sadece POST istekleri kabul edilir'
        ], 405);
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    $action = $input['action'] ?? '';
    
    // Action'a göre işlem yap
    switch ($action) {
        case 'generate_otp':
            generateOTP($input);
            break;
            
        case 'verify_otp':
            verifyOTP($input);
            break;
            
        case 'reset_password':
            resetPassword($input);
            break;
            
        case 'get_otp_list':
            getOTPList($input);
            break;
            
        case 'delete_otp':
            deleteOTP($input);
            break;
            
        default:
            sendJSONResponse([
                'success' => false,
                'message' => 'Geçersiz işlem'
            ], 400);
    }
    
} catch (PDOException $e) {
    logError('OTP API database error: ' . $e->getMessage());
    sendJSONResponse([
        'success' => false,
        'message' => 'Veritabanı hatası'
    ], 500);
} catch (Exception $e) {
    logError('OTP API general error: ' . $e->getMessage());
    sendJSONResponse([
        'success' => false,
        'message' => 'Sistem hatası'
    ], 500);
}

// OTP oluştur
function generateOTP($data) {
    global $pdo;
    
    $username = trim($data['username'] ?? '');
    
    if (empty($username)) {
        sendJSONResponse([
            'success' => false,
            'message' => 'Kullanıcı adı gerekli'
        ], 400);
    }
    
    // Kullanıcının var olup olmadığını kontrol et
    $stmt = $pdo->prepare("SELECT id, username, email FROM users WHERE username = ? AND is_active = 1");
    $stmt->execute([$username]);
    $user = $stmt->fetch();
    
    if (!$user) {
        sendJSONResponse([
            'success' => false,
            'message' => 'Kullanıcı bulunamadı veya aktif değil'
        ], 404);
    }
    
    // Eski OTP'leri sil (aynı kullanıcı için)
    $stmt = $pdo->prepare("DELETE FROM password_reset_otp WHERE username = ?");
    $stmt->execute([$username]);
    
    // 3 haneli rastgele OTP oluştur
    $otpCode = str_pad(rand(0, 999), 3, '0', STR_PAD_LEFT);
    
    // OTP'yi veritabanına kaydet (3 dakika geçerli)
    $expiresAt = date('Y-m-d H:i:s', time() + (3 * 60)); // 3 dakika
    $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
    
    $stmt = $pdo->prepare("
        INSERT INTO password_reset_otp (username, otp_code, expires_at, ip_address, user_agent) 
        VALUES (?, ?, ?, ?, ?)
    ");
    $stmt->execute([$username, $otpCode, $expiresAt, $ipAddress, $userAgent]);
    
    sendJSONResponse([
        'success' => true,
        'message' => 'OTP kodu oluşturuldu',
        'otp_code' => $otpCode,
        'expires_at' => $expiresAt,
        'username' => $username
    ]);
}

// OTP doğrula
function verifyOTP($data) {
    global $pdo;
    
    $username = trim($data['username'] ?? '');
    $otpCode = trim($data['otp_code'] ?? '');
    
    if (empty($username) || empty($otpCode)) {
        sendJSONResponse([
            'success' => false,
            'message' => 'Kullanıcı adı ve OTP kodu gerekli'
        ], 400);
    }
    
    // OTP'yi kontrol et
    $stmt = $pdo->prepare("
        SELECT id, expires_at, used 
        FROM password_reset_otp 
        WHERE username = ? AND otp_code = ? 
        ORDER BY created_at DESC 
        LIMIT 1
    ");
    $stmt->execute([$username, $otpCode]);
    $otp = $stmt->fetch();
    
    if (!$otp) {
        sendJSONResponse([
            'success' => false,
            'message' => 'Geçersiz OTP kodu'
        ], 400);
    }
    
    if ($otp['used']) {
        sendJSONResponse([
            'success' => false,
            'message' => 'Bu OTP kodu daha önce kullanılmış'
        ], 400);
    }
    
    // Süre kontrolü
    if (strtotime($otp['expires_at']) < time()) {
        sendJSONResponse([
            'success' => false,
            'message' => 'OTP kodunun süresi dolmuş'
        ], 400);
    }
    
    sendJSONResponse([
        'success' => true,
        'message' => 'OTP kodu doğrulandı',
        'otp_id' => $otp['id']
    ]);
}

// Şifre sıfırla
function resetPassword($data) {
    global $pdo;
    
    $username = trim($data['username'] ?? '');
    $otpCode = trim($data['otp_code'] ?? '');
    $newPassword = $data['new_password'] ?? '';
    
    if (empty($username) || empty($otpCode) || empty($newPassword)) {
        sendJSONResponse([
            'success' => false,
            'message' => 'Tüm alanlar gerekli'
        ], 400);
    }
    
    if (strlen($newPassword) < 6) {
        sendJSONResponse([
            'success' => false,
            'message' => 'Şifre en az 6 karakter olmalı'
        ], 400);
    }
    
    // OTP'yi tekrar doğrula
    $stmt = $pdo->prepare("
        SELECT id, expires_at, used 
        FROM password_reset_otp 
        WHERE username = ? AND otp_code = ? 
        ORDER BY created_at DESC 
        LIMIT 1
    ");
    $stmt->execute([$username, $otpCode]);
    $otp = $stmt->fetch();
    
    if (!$otp || $otp['used'] || strtotime($otp['expires_at']) < time()) {
        sendJSONResponse([
            'success' => false,
            'message' => 'Geçersiz veya süresi dolmuş OTP kodu'
        ], 400);
    }
    
    // Şifreyi hashle
    $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
    
    // Transaction başlat
    $pdo->beginTransaction();
    
    try {
        // Kullanıcının şifresini güncelle
        $stmt = $pdo->prepare("UPDATE users SET password_hash = ? WHERE username = ?");
        $stmt->execute([$hashedPassword, $username]);
        
        // OTP'yi kullanılmış olarak işaretle
        $stmt = $pdo->prepare("UPDATE password_reset_otp SET used = 1 WHERE id = ?");
        $stmt->execute([$otp['id']]);
        
        $pdo->commit();
        
        sendJSONResponse([
            'success' => true,
            'message' => 'Şifre başarıyla sıfırlandı'
        ]);
        
    } catch (Exception $e) {
        $pdo->rollBack();
        throw $e;
    }
}

// OTP listesini getir (Admin için)
function getOTPList($data) {
    global $pdo;
    
    // Basit admin kontrolü (gerçek uygulamada daha güvenli olmalı)
    $limit = $data['limit'] ?? 50;
    
    $stmt = $pdo->prepare("
        SELECT 
            id,
            username,
            otp_code,
            created_at,
            expires_at,
            used,
            ip_address,
            CASE 
                WHEN expires_at < NOW() THEN 'expired'
                WHEN used = 1 THEN 'used'
                ELSE 'active'
            END as status
        FROM password_reset_otp 
        ORDER BY created_at DESC 
        LIMIT ?
    ");
    $stmt->execute([$limit]);
    $otps = $stmt->fetchAll();
    
    sendJSONResponse([
        'success' => true,
        'otps' => $otps,
        'count' => count($otps)
    ]);
}

// OTP sil
function deleteOTP($data) {
    global $pdo;
    
    $otpId = $data['otp_id'] ?? 0;
    
    if (!$otpId) {
        sendJSONResponse([
            'success' => false,
            'message' => 'OTP ID gerekli'
        ], 400);
    }
    
    $stmt = $pdo->prepare("DELETE FROM password_reset_otp WHERE id = ?");
    $stmt->execute([$otpId]);
    
    sendJSONResponse([
        'success' => true,
        'message' => 'OTP silindi'
    ]);
}
?>
