<?php
require_once 'config.php';

// Güvenli session başlat
startSecureSession();

// JSON header'ı ayarla
header('Content-Type: application/json; charset=utf-8');
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');

// Admin yetkisi kontrolü
if (!isset($_SESSION['user_id']) || !isset($_SESSION['username'])) {
    sendJSONResponse([
        'success' => false,
        'message' => 'Oturum bulunamadı'
    ], 401);
}

try {
    // Kullanıcının admin olup olmadığını kontrol et
    $stmt = $pdo->prepare("SELECT role FROM users WHERE id = ? AND is_active = 1");
    $stmt->execute([$_SESSION['user_id']]);
    $user = $stmt->fetch();
    
    if (!$user || $user['role'] !== 'admin') {
        sendJSONResponse([
            'success' => false,
            'message' => 'Admin yetkisi gerekli'
        ], 403);
    }
    
    // İstek metodunu kontrol et
    $method = $_SERVER['REQUEST_METHOD'];
    $action = '';
    
    if ($method === 'GET') {
        $action = $_GET['action'] ?? '';
    } elseif ($method === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);
        $action = $input['action'] ?? '';
    }
    
    // Action'a göre işlem yap
    switch ($action) {
        case 'dashboard_stats':
            getDashboardStats();
            break;
            
        case 'recent_activity':
            getRecentActivity();
            break;
            
        case 'get_users':
            getUsers();
            break;
            
        case 'create_user':
            createUser($input);
            break;
            
        case 'delete_user':
            deleteUser($input);
            break;
            
        case 'get_logs':
            getLogs();
            break;
            
        case 'security_stats':
            getSecurityStats();
            break;
            
        case 'clear_old_logs':
            clearOldLogs();
            break;
            
        default:
            sendJSONResponse([
                'success' => false,
                'message' => 'Geçersiz işlem'
            ], 400);
    }
    
} catch (PDOException $e) {
    logError('Admin API database error: ' . $e->getMessage());
    sendJSONResponse([
        'success' => false,
        'message' => 'Veritabanı hatası'
    ], 500);
} catch (Exception $e) {
    logError('Admin API general error: ' . $e->getMessage());
    sendJSONResponse([
        'success' => false,
        'message' => 'Sistem hatası'
    ], 500);
}

// Dashboard istatistiklerini al
function getDashboardStats() {
    global $pdo;
    
    // Toplam kullanıcı sayısı
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM users");
    $totalUsers = $stmt->fetch()['total'];
    
    // Aktif kullanıcı sayısı
    $stmt = $pdo->query("SELECT COUNT(*) as active FROM users WHERE is_active = 1");
    $activeUsers = $stmt->fetch()['active'];
    
    // Bugünkü giriş sayısı
    $stmt = $pdo->query("
        SELECT COUNT(*) as today_logins 
        FROM login_attempts 
        WHERE success = 1 AND DATE(attempt_time) = CURDATE()
    ");
    $todayLogins = $stmt->fetch()['today_logins'];
    
    // Bugünkü başarısız deneme sayısı
    $stmt = $pdo->query("
        SELECT COUNT(*) as failed_attempts 
        FROM login_attempts 
        WHERE success = 0 AND DATE(attempt_time) = CURDATE()
    ");
    $failedAttempts = $stmt->fetch()['failed_attempts'];
    
    sendJSONResponse([
        'success' => true,
        'stats' => [
            'total_users' => $totalUsers,
            'active_users' => $activeUsers,
            'today_logins' => $todayLogins,
            'failed_attempts' => $failedAttempts
        ]
    ]);
}

// Son aktiviteleri al
function getRecentActivity() {
    global $pdo;
    
    $stmt = $pdo->prepare("
        SELECT sl.*, u.username 
        FROM system_logs sl
        LEFT JOIN users u ON sl.user_id = u.id
        ORDER BY sl.created_at DESC
        LIMIT 10
    ");
    $stmt->execute();
    $activities = $stmt->fetchAll();
    
    sendJSONResponse([
        'success' => true,
        'activities' => $activities
    ]);
}

// Kullanıcıları al
function getUsers() {
    global $pdo;
    
    $page = (int)($_GET['page'] ?? 1);
    $limit = (int)($_GET['limit'] ?? 10);
    $search = $_GET['search'] ?? '';
    $role = $_GET['role'] ?? '';
    $status = $_GET['status'] ?? '';
    
    $offset = ($page - 1) * $limit;
    
    // WHERE koşulları oluştur
    $whereConditions = [];
    $params = [];
    
    if (!empty($search)) {
        $whereConditions[] = "(username LIKE ? OR email LIKE ? OR first_name LIKE ? OR last_name LIKE ?)";
        $searchTerm = "%{$search}%";
        $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm, $searchTerm]);
    }
    
    if (!empty($role)) {
        $whereConditions[] = "role = ?";
        $params[] = $role;
    }
    
    if ($status !== '') {
        $whereConditions[] = "is_active = ?";
        $params[] = (int)$status;
    }
    
    $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
    
    // Toplam sayıyı al
    $countQuery = "SELECT COUNT(*) as total FROM users {$whereClause}";
    $stmt = $pdo->prepare($countQuery);
    $stmt->execute($params);
    $totalUsers = $stmt->fetch()['total'];
    
    // Kullanıcıları al
    $query = "
        SELECT id, username, email, first_name, last_name, role, is_active, last_login, created_at
        FROM users 
        {$whereClause}
        ORDER BY created_at DESC
        LIMIT {$limit} OFFSET {$offset}
    ";
    
    $stmt = $pdo->prepare($query);
    $stmt->execute($params);
    $users = $stmt->fetchAll();
    
    $totalPages = ceil($totalUsers / $limit);
    
    sendJSONResponse([
        'success' => true,
        'users' => $users,
        'pagination' => [
            'current_page' => $page,
            'total_pages' => $totalPages,
            'total_users' => $totalUsers,
            'per_page' => $limit
        ]
    ]);
}

// Yeni kullanıcı oluştur
function createUser($data) {
    global $pdo;
    
    // Gerekli alanları kontrol et
    $required = ['username', 'email', 'password', 'role'];
    foreach ($required as $field) {
        if (empty($data[$field])) {
            sendJSONResponse([
                'success' => false,
                'message' => ucfirst($field) . ' alanı gereklidir!'
            ], 400);
        }
    }
    
    // Kullanıcı adı ve e-posta benzersizliği kontrolü
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM users WHERE username = ? OR email = ?");
    $stmt->execute([$data['username'], $data['email']]);
    $exists = $stmt->fetch()['count'];
    
    if ($exists > 0) {
        sendJSONResponse([
            'success' => false,
            'message' => 'Bu kullanıcı adı veya e-posta zaten kullanılıyor!'
        ], 400);
    }
    
    // Şifreyi hashle
    $passwordHash = password_hash($data['password'], PASSWORD_DEFAULT);
    
    // Kullanıcıyı oluştur
    $stmt = $pdo->prepare("
        INSERT INTO users (username, email, password_hash, first_name, last_name, phone, role, is_active) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    ");
    
    $stmt->execute([
        $data['username'],
        $data['email'],
        $passwordHash,
        $data['first_name'] ?? null,
        $data['last_name'] ?? null,
        $data['phone'] ?? null,
        $data['role'],
        $data['is_active'] ? 1 : 0
    ]);
    
    $userId = $pdo->lastInsertId();
    
    // Sistem loguna kaydet
    $stmt = $pdo->prepare("
        INSERT INTO system_logs (user_id, action, description, ip_address) 
        VALUES (?, 'user_created', ?, ?)
    ");
    $stmt->execute([
        $_SESSION['user_id'],
        "Yeni kullanıcı oluşturuldu: {$data['username']}",
        getClientIP()
    ]);
    
    sendJSONResponse([
        'success' => true,
        'message' => 'Kullanıcı başarıyla oluşturuldu!',
        'user_id' => $userId
    ]);
}

// Kullanıcı sil
function deleteUser($data) {
    global $pdo;
    
    $userId = $data['user_id'] ?? null;
    
    if (!$userId) {
        sendJSONResponse([
            'success' => false,
            'message' => 'Kullanıcı ID gereklidir!'
        ], 400);
    }
    
    // Kendi hesabını silmeye çalışıyor mu?
    if ($userId == $_SESSION['user_id']) {
        sendJSONResponse([
            'success' => false,
            'message' => 'Kendi hesabınızı silemezsiniz!'
        ], 400);
    }
    
    // Kullanıcı bilgilerini al
    $stmt = $pdo->prepare("SELECT username FROM users WHERE id = ?");
    $stmt->execute([$userId]);
    $user = $stmt->fetch();
    
    if (!$user) {
        sendJSONResponse([
            'success' => false,
            'message' => 'Kullanıcı bulunamadı!'
        ], 404);
    }
    
    // Kullanıcıyı sil
    $stmt = $pdo->prepare("DELETE FROM users WHERE id = ?");
    $stmt->execute([$userId]);
    
    // Sistem loguna kaydet
    $stmt = $pdo->prepare("
        INSERT INTO system_logs (user_id, action, description, ip_address) 
        VALUES (?, 'user_deleted', ?, ?)
    ");
    $stmt->execute([
        $_SESSION['user_id'],
        "Kullanıcı silindi: {$user['username']}",
        getClientIP()
    ]);
    
    sendJSONResponse([
        'success' => true,
        'message' => 'Kullanıcı başarıyla silindi!'
    ]);
}

// Sistem loglarını al
function getLogs() {
    global $pdo;
    
    $stmt = $pdo->prepare("
        SELECT sl.*, u.username 
        FROM system_logs sl
        LEFT JOIN users u ON sl.user_id = u.id
        ORDER BY sl.created_at DESC
        LIMIT 100
    ");
    $stmt->execute();
    $logs = $stmt->fetchAll();
    
    sendJSONResponse([
        'success' => true,
        'logs' => $logs
    ]);
}

// Güvenlik istatistiklerini al
function getSecurityStats() {
    global $pdo;
    
    // Son 24 saatteki başarısız giriş denemeleri
    $stmt = $pdo->query("
        SELECT COUNT(*) as failed_logins 
        FROM login_attempts 
        WHERE success = 0 AND attempt_time > DATE_SUB(NOW(), INTERVAL 24 HOUR)
    ");
    $failedLogins = $stmt->fetch()['failed_logins'];
    
    // Kilitli hesap sayısı
    $stmt = $pdo->query("
        SELECT COUNT(*) as locked_accounts 
        FROM users 
        WHERE locked_until > NOW()
    ");
    $lockedAccounts = $stmt->fetch()['locked_accounts'];
    
    // Son 7 gündeki benzersiz başarısız IP sayısı
    $stmt = $pdo->query("
        SELECT COUNT(DISTINCT ip_address) as suspicious_ips 
        FROM login_attempts 
        WHERE success = 0 AND attempt_time > DATE_SUB(NOW(), INTERVAL 7 DAY)
    ");
    $suspiciousIPs = $stmt->fetch()['suspicious_ips'];
    
    // Son başarısız denemeler
    $stmt = $pdo->query("
        SELECT ip_address, username, COUNT(*) as attempt_count, MAX(attempt_time) as attempt_time
        FROM login_attempts 
        WHERE success = 0 AND attempt_time > DATE_SUB(NOW(), INTERVAL 24 HOUR)
        GROUP BY ip_address, username
        ORDER BY attempt_count DESC, attempt_time DESC
        LIMIT 20
    ");
    $failedAttempts = $stmt->fetchAll();
    
    sendJSONResponse([
        'success' => true,
        'stats' => [
            'failed_logins' => $failedLogins,
            'locked_accounts' => $lockedAccounts,
            'suspicious_ips' => $suspiciousIPs
        ],
        'failed_attempts' => $failedAttempts
    ]);
}

// Eski logları temizle
function clearOldLogs() {
    global $pdo;
    
    // 90 günden eski logları sil
    $stmt = $pdo->prepare("DELETE FROM system_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 90 DAY)");
    $stmt->execute();
    $deletedLogs = $stmt->rowCount();
    
    // 7 günden eski login denemelerini sil
    $stmt = $pdo->prepare("DELETE FROM login_attempts WHERE attempt_time < DATE_SUB(NOW(), INTERVAL 7 DAY)");
    $stmt->execute();
    $deletedAttempts = $stmt->rowCount();
    
    // Sistem loguna kaydet
    $stmt = $pdo->prepare("
        INSERT INTO system_logs (user_id, action, description, ip_address) 
        VALUES (?, 'logs_cleared', ?, ?)
    ");
    $stmt->execute([
        $_SESSION['user_id'],
        "Eski loglar temizlendi: {$deletedLogs} log, {$deletedAttempts} deneme",
        getClientIP()
    ]);
    
    sendJSONResponse([
        'success' => true,
        'message' => "Temizlendi: {$deletedLogs} log, {$deletedAttempts} deneme"
    ]);
}
?>
