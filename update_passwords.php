<?php
require_once 'config.php';

echo "<h1><PERSON><PERSON><PERSON></h1>";

try {
    // Yeni hash oluştur
    $password = '123456';
    $newHash = password_hash($password, PASSWORD_DEFAULT);
    
    echo "<p><strong>Yeni Hash:</strong> " . $newHash . "</p>";
    echo "<p><strong>Hash Test:</strong> " . (password_verify($password, $newHash) ? 'BAŞARILI' : 'BAŞARISIZ') . "</p>";
    
    // Admin kullanıcısını güncelle
    $stmt = $pdo->prepare("UPDATE users SET password_hash = ? WHERE username = 'admin'");
    $adminResult = $stmt->execute([$newHash]);
    
    // Demo kullanıcısını güncelle
    $stmt = $pdo->prepare("UPDATE users SET password_hash = ? WHERE username = 'demo'");
    $demoResult = $stmt->execute([$newHash]);
    
    if ($adminResult && $demoResult) {
        echo "<p style='color: green; font-weight: bold;'>✓ Şifreler başarıyla güncellendi!</p>";
        
        // Test et
        echo "<h2>Güncelleme Testi:</h2>";
        
        $users = ['admin', 'demo'];
        foreach ($users as $username) {
            $stmt = $pdo->prepare("SELECT password_hash FROM users WHERE username = ?");
            $stmt->execute([$username]);
            $user = $stmt->fetch();
            
            if ($user) {
                $testResult = password_verify($password, $user['password_hash']);
                echo "<p><strong>{$username}:</strong> " . ($testResult ? '<span style="color: green;">BAŞARILI</span>' : '<span style="color: red;">BAŞARISIZ</span>') . "</p>";
            }
        }
        
        echo "<hr>";
        echo "<p style='color: blue; font-weight: bold;'>Artık admin/123456 ve demo/123456 ile giriş yapabilirsiniz!</p>";
        echo "<p><a href='index.html'>Login Sayfasına Git</a> | <a href='login_test.php'>Login Test</a></p>";
        
    } else {
        echo "<p style='color: red; font-weight: bold;'>✗ Şifre güncelleme başarısız!</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>Hata:</strong> " . $e->getMessage() . "</p>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 40px;
    background: #f5f5f5;
}

h1, h2 {
    color: #333;
}

p {
    background: white;
    padding: 10px;
    border-radius: 4px;
    margin: 10px 0;
}

a {
    color: #007cba;
    text-decoration: none;
    font-weight: bold;
}

a:hover {
    text-decoration: underline;
}
</style>
