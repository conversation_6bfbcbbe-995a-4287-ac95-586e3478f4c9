<!DOCTYPE html>
<html>
<head>
    <title>Hash Test</title>
</head>
<body>
    <h1>Şifre Hash Test</h1>
    
    <?php
    if ($_POST) {
        $password = $_POST['password'];
        $hash = password_hash($password, PASSWORD_DEFAULT);
        
        echo "<h2>Sonuçlar:</h2>";
        echo "<p><strong>Şifre:</strong> " . htmlspecialchars($password) . "</p>";
        echo "<p><strong>Hash:</strong> " . htmlspecialchars($hash) . "</p>";
        echo "<p><strong>Do<PERSON><PERSON>lama:</strong> " . (password_verify($password, $hash) ? 'BAŞARILI' : 'BAŞARISIZ') . "</p>";
        
        // Mevcut hash'leri test et
        $testHashes = [
            '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
            '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm'
        ];
        
        echo "<h3>Mevcut Hash'ler Test:</h3>";
        foreach ($testHashes as $i => $testHash) {
            $result = password_verify($password, $testHash);
            echo "<p>Hash " . ($i+1) . ": " . ($result ? 'BAŞARILI' : 'BAŞARISIZ') . "</p>";
        }
        
        echo "<h3>SQL Update:</h3>";
        echo "<pre>UPDATE users SET password_hash = '" . $hash . "' WHERE username IN ('admin', 'demo');</pre>";
    }
    ?>
    
    <form method="post">
        <p>
            <label>Şifre:</label><br>
            <input type="text" name="password" value="123456" required>
        </p>
        <p>
            <input type="submit" value="Hash Oluştur">
        </p>
    </form>
</body>
</html>
