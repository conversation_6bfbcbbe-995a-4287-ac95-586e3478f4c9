<?php
// Test hash oluşturma
$password = '123456';
$hash = password_hash($password, PASSWORD_DEFAULT);

echo "Şifre: " . $password . "\n";
echo "Hash: " . $hash . "\n";
echo "Do<PERSON><PERSON>lama: " . (password_verify($password, $hash) ? 'BAŞARILI' : 'BAŞARISIZ') . "\n";

// Mevcut hash'i test et
$existingHash = '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm';
echo "\nMevcut hash test: " . (password_verify($password, $existingHash) ? 'BAŞARILI' : 'BAŞARISIZ') . "\n";

// Yeni hash oluştur
$newHash = password_hash($password, PASSWORD_DEFAULT);
echo "Yeni hash: " . $newHash . "\n";
?>
