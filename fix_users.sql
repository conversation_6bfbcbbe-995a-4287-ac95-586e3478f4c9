-- =====================================================
-- KULLANICI ŞİFRE DÜZELTME VE SİSTEM KURULUM SCRIPTI
-- =====================================================
-- Bu script tüm kullanıcı sorunlarını çözer ve sistemi hazır hale getirir
-- Şifre: 123456 (Tüm kullanıcılar için)

-- 1. MEVCUT KULLANICILARIN ŞİFRELERİNİ GÜNCELLE
-- Test edilmiş ve çalışan hash kullanılıyor
UPDATE users SET password_hash = '$2y$10$1.uOLZznQY/vJjE9o2IOyuqMRxA1DpdwPaUiyii4vY1CNRUt62gHq' WHERE username = 'admin';
UPDATE users SET password_hash = '$2y$10$1.uOLZznQY/vJjE9o2IOyuqMRxA1DpdwPaUiyii4vY1CNRUt62gHq' WHERE username = 'demo';

-- 2. EĞER KULLANICILAR YOKSA YENİDEN OLUŞTUR
-- INSERT IGNORE kullanarak mevcut kayıtları koruyoruz
INSERT IGNORE INTO `users` (`username`, `email`, `password_hash`, `first_name`, `last_name`, `phone`, `bio`, `is_active`, `is_verified`, `role`, `created_at`) VALUES
('admin', '<EMAIL>', '$2y$10$1.uOLZznQY/vJjE9o2IOyuqMRxA1DpdwPaUiyii4vY1CNRUt62gHq', 'Admin', 'User', NULL, 'Sistem yöneticisi', 1, 1, 'admin', NOW()),
('demo', '<EMAIL>', '$2y$10$1.uOLZznQY/vJjE9o2IOyuqMRxA1DpdwPaUiyii4vY1CNRUt62gHq', 'Demo', 'User', NULL, 'Demo kullanıcısı', 1, 1, 'user', NOW());

-- 3. KULLANICI DURUMLARINI AKTİF YAP
UPDATE users SET is_active = 1, is_verified = 1 WHERE username IN ('admin', 'demo');

-- 4. BAŞARISIZ GİRİŞ DENEMELERİNİ SIFIRLA
UPDATE users SET failed_login_attempts = 0, locked_until = NULL WHERE username IN ('admin', 'demo');

-- 5. SON GİRİŞ TARİHLERİNİ GÜNCELLE
UPDATE users SET last_login = NOW() WHERE username IN ('admin', 'demo');

-- 6. REMEMBER TOKEN'LARI TEMİZLE (GÜVENLİK İÇİN)
DELETE FROM remember_tokens WHERE user_id IN (
    SELECT id FROM users WHERE username IN ('admin', 'demo')
);

-- 7. SİSTEM LOGLARINI TEMİZLE (İSTEĞE BAĞLI)
-- DELETE FROM system_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);

-- 8. TEST VERİLERİ - ÖRNEK ÜRÜNLER EKLE
INSERT IGNORE INTO `product` (`code`, `name`, `unit`, `store_code`, `condition_type`, `price_type`, `currency`, `unit_price`, `unit_qualification`, `campaign_id`) VALUES
('PRD001', 'EKMEK BÜYÜK', 'ADET', 'MG001', 'NORMAL', 'SATIŞ', 'TRY', 2.50, 'STANDART', ''),
('PRD002', 'SÜT 1LT', 'ADET', 'MG001', 'SOĞUK', 'SATIŞ', 'TRY', 8.75, 'STANDART', ''),
('PRD003', 'YUMURTA 30LU', 'PAKET', 'MG001', 'NORMAL', 'SATIŞ', 'TRY', 45.00, 'STANDART', 'CMP001'),
('PRD004', 'DOMATES KG', 'KG', 'MG001', 'TAZE', 'SATIŞ', 'TRY', 12.50, 'STANDART', ''),
('PRD005', 'ELMA KG', 'KG', 'MG001', 'TAZE', 'SATIŞ', 'TRY', 15.75, 'STANDART', '');

-- 9. TEST VERİLERİ - ÖRNEK BARKODLAR EKLE
INSERT IGNORE INTO `barcode` (`id`, `code`, `product_code`) VALUES
('BRC001', '8690123456789', 'PRD001'),
('BRC002', '8690123456790', 'PRD001'),
('BRC003', '8690987654321', 'PRD002'),
('BRC004', '8690987654322', 'PRD002'),
('BRC005', '8690555666777', 'PRD003'),
('BRC006', '8690111222333', 'PRD004'),
('BRC007', '8690444555666', 'PRD005');

-- 10. VERİTABANI BÜTÜNLÜĞÜNÜ KONTROL ET
-- Foreign key constraint'leri kontrol et
SET foreign_key_checks = 1;

-- 11. KULLANICI YETKİLERİNİ KONTROL ET
-- Admin kullanıcısının admin yetkisi olduğundan emin ol
UPDATE users SET role = 'admin' WHERE username = 'admin';
UPDATE users SET role = 'user' WHERE username = 'demo';

-- 12. OTP TABLOSUNU OLUŞTUR (Şifre sıfırlama için)
CREATE TABLE IF NOT EXISTS `password_reset_otp` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `username` varchar(255) NOT NULL,
    `otp_code` varchar(6) NOT NULL,
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `expires_at` timestamp NOT NULL,
    `used` tinyint(1) DEFAULT 0,
    `ip_address` varchar(45) DEFAULT NULL,
    `user_agent` text DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `idx_username` (`username`),
    KEY `idx_otp_code` (`otp_code`),
    KEY `idx_expires_at` (`expires_at`),
    KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 13. EXPIRY_TRACKING TABLOSUNA YENİ KOLONLAR EKLE
ALTER TABLE `expiry_tracking`
ADD COLUMN `product_image_url` varchar(500) DEFAULT NULL AFTER `quantity`,
ADD COLUMN `notes` text DEFAULT NULL AFTER `product_image_url`;

-- 14. BAŞARILI KURULUM MESAJI
-- Bu kısım sadece bilgi amaçlı, SQL çalıştırıldığında gösterilmez
-- Kurulum tamamlandı!
--
-- GİRİŞ BİLGİLERİ:
-- Admin: admin / 123456
-- Demo:  demo / 123456
--
-- HASH: $2y$10$1.uOLZznQY/vJjE9o2IOyuqMRxA1DpdwPaUiyii4vY1CNRUt62gHq
--
-- TEST ÜRÜNLER:
-- - EKMEK BÜYÜK (PRD001) - Barkod: 8690123456789
-- - SÜT 1LT (PRD002) - Barkod: 8690987654321
-- - YUMURTA 30LU (PRD003) - Barkod: 8690555666777
-- - DOMATES KG (PRD004) - Barkod: 8690111222333
-- - ELMA KG (PRD005) - Barkod: 8690444555666
--
-- ŞİFRE SIFIRLAMA:
-- - Login sayfasında "Şifremi Unuttum" linkine tıklayın
-- - Kullanıcı adınızı girin
-- - Admin panelinde OTP kodunu görün
-- - 3 haneli kodu girin ve şifrenizi sıfırlayın
--
-- SİSTEM HAZIR!
