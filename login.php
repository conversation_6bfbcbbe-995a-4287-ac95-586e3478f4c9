<?php
require_once 'config.php';

// Güvenli session başlat
startSecureSession();

// Sadece POST isteklerini kabul et
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendJSONResponse([
        'success' => false,
        'message' => 'Geçersiz istek metodu!'
    ], 405);
}

// Content-Type kontrolü
$contentType = $_SERVER['CONTENT_TYPE'] ?? '';
if (strpos($contentType, 'application/json') === false) {
    sendJSONResponse([
        'success' => false,
        'message' => 'Geçersiz content type!'
    ], 400);
}

// JSON verisini al
$input = json_decode(file_get_contents('php://input'), true);

if (json_last_error() !== JSON_ERROR_NONE) {
    sendJSONResponse([
        'success' => false,
        'message' => 'Geçersiz JSON verisi!'
    ], 400);
}

// Gerekli alanları kontrol et
if (!isset($input['username']) || !isset($input['password'])) {
    sendJSONResponse([
        'success' => false,
        'message' => 'Kullanıcı adı ve şifre gereklidir!'
    ], 400);
}

$username = sanitizeInput($input['username']);
$password = $input['password'];
$remember = isset($input['remember']) ? (bool)$input['remember'] : false;

// Boş alan kontrolü
if (empty($username) || empty($password)) {
    sendJSONResponse([
        'success' => false,
        'message' => 'Kullanıcı adı ve şifre boş olamaz!'
    ], 400);
}

// Minimum uzunluk kontrolü
if (strlen($username) < 3) {
    sendJSONResponse([
        'success' => false,
        'message' => 'Kullanıcı adı en az 3 karakter olmalıdır!'
    ], 400);
}

if (strlen($password) < 6) {
    sendJSONResponse([
        'success' => false,
        'message' => 'Şifre en az 6 karakter olmalıdır!'
    ], 400);
}

// IP adresini al
$clientIP = getClientIP();

// Rate limiting kontrolü
$rateLimitCheck = checkRateLimit($clientIP);
if ($rateLimitCheck['blocked']) {
    $minutes = ceil($rateLimitCheck['time_left'] / 60);
    sendJSONResponse([
        'success' => false,
        'message' => "Çok fazla başarısız deneme! {$minutes} dakika sonra tekrar deneyin."
    ], 429);
}

try {
    // Kullanıcıyı veritabanından bul
    $stmt = $pdo->prepare("
        SELECT id, username, password_hash, email, is_active, last_login, 
               failed_login_attempts, locked_until
        FROM users 
        WHERE username = ? OR email = ?
    ");
    $stmt->execute([$username, $username]);
    $user = $stmt->fetch();

    // Kullanıcı bulunamadı
    if (!$user) {
        error_log("User not found for username: {$username}");
        logLoginAttempt($clientIP, $username, false);
        sendJSONResponse([
            'success' => false,
            'message' => 'Kullanıcı adı veya şifre hatalı!'
        ], 401);
    }

    error_log("User found - ID: {$user['id']}, Username: {$user['username']}, Active: {$user['is_active']}");

    // Hesap aktif değil
    if (!$user['is_active']) {
        logLoginAttempt($clientIP, $username, false);
        sendJSONResponse([
            'success' => false,
            'message' => 'Hesabınız aktif değil! Lütfen yönetici ile iletişime geçin.'
        ], 401);
    }

    // Hesap kilitli mi kontrol et
    if ($user['locked_until'] && strtotime($user['locked_until']) > time()) {
        $unlockTime = date('H:i', strtotime($user['locked_until']));
        logLoginAttempt($clientIP, $username, false);
        sendJSONResponse([
            'success' => false,
            'message' => "Hesabınız kilitli! Kilit açılma saati: {$unlockTime}"
        ], 401);
    }

    // Debug: Şifre kontrolü için log
    error_log("Login attempt - Username: {$username}, Password length: " . strlen($password) . ", Hash: " . substr($user['password_hash'], 0, 20) . "...");

    // Şifre kontrolü (hem Argon2ID hem de bcrypt destekli)
    $passwordValid = password_verify($password, $user['password_hash']);
    error_log("Password verification result: " . ($passwordValid ? 'SUCCESS' : 'FAILED'));

    if (!$passwordValid) {
        // Başarısız deneme sayısını artır
        $failedAttempts = $user['failed_login_attempts'] + 1;
        $lockUntil = null;
        
        // 5 başarısız denemeden sonra hesabı kilitle
        if ($failedAttempts >= 5) {
            $lockUntil = date('Y-m-d H:i:s', time() + 1800); // 30 dakika kilitle
        }
        
        $stmt = $pdo->prepare("
            UPDATE users 
            SET failed_login_attempts = ?, locked_until = ?
            WHERE id = ?
        ");
        $stmt->execute([$failedAttempts, $lockUntil, $user['id']]);
        
        logLoginAttempt($clientIP, $username, false);

        $remainingAttempts = 5 - $failedAttempts;
        if ($remainingAttempts > 0) {
            sendJSONResponse([
                'success' => false,
                'message' => "Kullanıcı adı veya şifre hatalı! Kalan deneme hakkı: {$remainingAttempts}"
            ], 401);
        } else {
            sendJSONResponse([
                'success' => false,
                'message' => 'Çok fazla başarısız deneme! Hesabınız 30 dakika kilitlendi.'
            ], 401);
        }
    }

    // Başarılı giriş
    // Session verilerini ayarla
    $_SESSION['user_id'] = $user['id'];
    $_SESSION['username'] = $user['username'];
    $_SESSION['email'] = $user['email'];
    $_SESSION['login_time'] = time();
    $_SESSION['last_activity'] = time();
    
    // Remember me özelliği
    if ($remember) {
        $token = generateToken();
        $expiry = date('Y-m-d H:i:s', time() + (30 * 24 * 60 * 60)); // 30 gün
        
        // Remember token'ı veritabanına kaydet
        $stmt = $pdo->prepare("
            INSERT INTO remember_tokens (user_id, token, expires_at) 
            VALUES (?, ?, ?)
            ON DUPLICATE KEY UPDATE token = VALUES(token), expires_at = VALUES(expires_at)
        ");
        $stmt->execute([$user['id'], hash('sha256', $token), $expiry]);
        
        // Cookie ayarla
        setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/', '', true, true);
    }

    // Kullanıcı bilgilerini güncelle
    $stmt = $pdo->prepare("
        UPDATE users 
        SET last_login = NOW(), failed_login_attempts = 0, locked_until = NULL
        WHERE id = ?
    ");
    $stmt->execute([$user['id']]);

    // Başarılı girişi logla
    logLoginAttempt($clientIP, $username, true);
    clearLoginAttempts($clientIP);

    // Başarılı response
    sendJSONResponse([
        'success' => true,
        'message' => 'Giriş başarılı!',
        'user' => [
            'id' => $user['id'],
            'username' => $user['username'],
            'email' => $user['email']
        ],
        'redirect' => 'dashboard.html'
    ]);

} catch (PDOException $e) {
    logError('Login database error: ' . $e->getMessage(), [
        'username' => $username,
        'ip' => $clientIP
    ]);
    
    sendJSONResponse([
        'success' => false,
        'message' => 'Sistem hatası! Lütfen daha sonra tekrar deneyin.'
    ], 500);
} catch (Exception $e) {
    logError('Login general error: ' . $e->getMessage(), [
        'username' => $username,
        'ip' => $clientIP
    ]);
    
    sendJSONResponse([
        'success' => false,
        'message' => 'Beklenmeyen bir hata oluştu!'
    ], 500);
}
?>
