<?php
require_once 'config.php';

echo "<h1>Son Kullanım Tarihi Test</h1>";

try {
    // Veritabanı bağlantısını test et
    echo "<h2>1. Veritabanı Bağlantısı</h2>";
    echo "<p style='color: green;'>✓ Veritabanı bağlantısı başarılı</p>";
    
    // Expiry_tracking tablosunun varlığını kontrol et
    echo "<h2>2. Tablo Kontrolü</h2>";
    $stmt = $pdo->query("SHOW TABLES LIKE 'expiry_tracking'");
    $tableExists = $stmt->fetch();
    
    if ($tableExists) {
        echo "<p style='color: green;'>✓ expiry_tracking tablosu mevcut</p>";
        
        // Tablo yapısını göster
        echo "<h3>Tablo Yapısı:</h3>";
        $stmt = $pdo->query("DESCRIBE expiry_tracking");
        $columns = $stmt->fetchAll();
        
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Sütun</th><th>Tip</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>{$column['Field']}</td>";
            echo "<td>{$column['Type']}</td>";
            echo "<td>{$column['Null']}</td>";
            echo "<td>{$column['Key']}</td>";
            echo "<td>{$column['Default']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Kayıt sayısını kontrol et
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM expiry_tracking");
        $count = $stmt->fetch()['count'];
        echo "<p>Toplam kayıt sayısı: <strong>{$count}</strong></p>";
        
        // Eğer kayıt varsa ilk 5'ini göster
        if ($count > 0) {
            echo "<h3>Örnek Kayıtlar:</h3>";
            $stmt = $pdo->query("SELECT * FROM expiry_tracking ORDER BY created_at DESC LIMIT 5");
            $records = $stmt->fetchAll();
            
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>ID</th><th>User ID</th><th>Ürün Kodu</th><th>Ürün Adı</th><th>Son Kullanım</th><th>Adet</th><th>Oluşturulma</th></tr>";
            foreach ($records as $record) {
                echo "<tr>";
                echo "<td>{$record['id']}</td>";
                echo "<td>{$record['user_id']}</td>";
                echo "<td>{$record['product_code']}</td>";
                echo "<td>{$record['product_name']}</td>";
                echo "<td>{$record['expiry_date']}</td>";
                echo "<td>{$record['quantity']}</td>";
                echo "<td>{$record['created_at']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
    } else {
        echo "<p style='color: red;'>✗ expiry_tracking tablosu bulunamadı!</p>";
        echo "<p><strong>Çözüm:</strong> database.sql dosyasını çalıştırın veya aşağıdaki SQL'i çalıştırın:</p>";
        echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px;'>";
        echo "CREATE TABLE IF NOT EXISTS `expiry_tracking` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) NOT NULL,
    `product_code` varchar(255) NOT NULL,
    `product_name` varchar(255) NOT NULL,
    `expiry_date` date NOT NULL,
    `quantity` int(11) NOT NULL DEFAULT 1,
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_product_code` (`product_code`),
    KEY `idx_expiry_date` (`expiry_date`),
    KEY `idx_user_expiry` (`user_id`, `expiry_date`),
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
        echo "</pre>";
    }
    
    // Session kontrolü
    echo "<h2>3. Session Kontrolü</h2>";
    session_start();
    if (isset($_SESSION['user_id'])) {
        echo "<p style='color: green;'>✓ Kullanıcı oturumu aktif (ID: {$_SESSION['user_id']})</p>";
    } else {
        echo "<p style='color: red;'>✗ Kullanıcı oturumu bulunamadı!</p>";
        echo "<p><strong>Çözüm:</strong> Önce giriş yapın</p>";
    }
    
    // API dosyasının varlığını kontrol et
    echo "<h2>4. API Dosyası Kontrolü</h2>";
    if (file_exists('expiry_api.php')) {
        echo "<p style='color: green;'>✓ expiry_api.php dosyası mevcut</p>";
    } else {
        echo "<p style='color: red;'>✗ expiry_api.php dosyası bulunamadı!</p>";
    }
    
    // Test verisi ekle
    echo "<h2>5. Test Verisi Ekleme</h2>";
    if (isset($_GET['add_test']) && isset($_SESSION['user_id'])) {
        $testData = [
            ['PRD001', 'Test Ürün 1', date('Y-m-d', strtotime('+2 days')), 5],
            ['PRD002', 'Test Ürün 2', date('Y-m-d', strtotime('+5 days')), 3],
            ['PRD003', 'Test Ürün 3', date('Y-m-d', strtotime('+10 days')), 2],
            ['PRD004', 'Test Ürün 4', date('Y-m-d', strtotime('-1 days')), 1],
        ];
        
        foreach ($testData as $data) {
            $stmt = $pdo->prepare("
                INSERT IGNORE INTO expiry_tracking (user_id, product_code, product_name, expiry_date, quantity) 
                VALUES (?, ?, ?, ?, ?)
            ");
            $stmt->execute([$_SESSION['user_id'], $data[0], $data[1], $data[2], $data[3]]);
        }
        
        echo "<p style='color: green;'>✓ Test verileri eklendi!</p>";
        echo "<p><a href='test_expiry.php'>Sayfayı Yenile</a></p>";
    } else {
        echo "<p><a href='test_expiry.php?add_test=1' style='background: #4CAF50; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Test Verisi Ekle</a></p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Hata: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='dashboard.html'>Dashboard'a Dön</a> | <a href='login_test.php'>Login Test</a></p>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 40px;
    background: #f5f5f5;
}

h1, h2, h3 {
    color: #333;
}

table {
    background: white;
    margin: 10px 0;
}

th, td {
    padding: 8px 12px;
    text-align: left;
}

th {
    background: #f0f0f0;
}

pre {
    overflow-x: auto;
}
</style>
