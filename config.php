<?php
// Veritabanı bağlantı ayarları
define('DB_HOST', 'localhost');
define('DB_NAME', 'ayl289fo_web');
define('DB_USER', 'ayl289fo_web');
define('DB_PASS', 'QpSfzfHVxQx67hPsq4Ma');

// Güvenlik ayarları
define('SESSION_TIMEOUT', 3600); // 1 saat
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 dakika

// Veritabanı bağlantısı
try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ]
    );
} catch (PDOException $e) {
    error_log("Veritabanı bağlantı hatası: " . $e->getMessage());
    die(json_encode([
        'success' => false,
        'message' => 'Veritabanı bağlantı hatası!'
    ]));
}

// Güvenlik fonksiyonları
function sanitizeInput($input) {
    return htmlspecialchars(strip_tags(trim($input)), ENT_QUOTES, 'UTF-8');
}

function generateToken($length = 32) {
    return bin2hex(random_bytes($length));
}

function hashPassword($password) {
    return password_hash($password, PASSWORD_ARGON2ID, [
        'memory_cost' => 65536,
        'time_cost' => 4,
        'threads' => 3
    ]);
}

function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

// Session güvenliği
function startSecureSession() {
    if (session_status() === PHP_SESSION_NONE) {
        ini_set('session.cookie_httponly', 1);
        ini_set('session.cookie_secure', 1);
        ini_set('session.use_strict_mode', 1);
        session_start();
        
        // Session hijacking koruması
        if (!isset($_SESSION['created'])) {
            $_SESSION['created'] = time();
        } else if (time() - $_SESSION['created'] > SESSION_TIMEOUT) {
            session_regenerate_id(true);
            $_SESSION['created'] = time();
        }
    }
}

// CSRF token oluştur
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = generateToken();
    }
    return $_SESSION['csrf_token'];
}

// CSRF token doğrula
function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

// Rate limiting
function checkRateLimit($ip, $action = 'login') {
    global $pdo;
    
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as attempts, MAX(attempt_time) as last_attempt 
        FROM login_attempts 
        WHERE ip_address = ? AND action = ? AND attempt_time > DATE_SUB(NOW(), INTERVAL ? SECOND)
    ");
    $stmt->execute([$ip, $action, LOGIN_LOCKOUT_TIME]);
    $result = $stmt->fetch();
    
    if ($result['attempts'] >= MAX_LOGIN_ATTEMPTS) {
        $timeLeft = LOGIN_LOCKOUT_TIME - (time() - strtotime($result['last_attempt']));
        if ($timeLeft > 0) {
            return [
                'blocked' => true,
                'time_left' => $timeLeft
            ];
        }
    }
    
    return ['blocked' => false];
}

// Login denemesi kaydet
function logLoginAttempt($ip, $username, $success, $action = 'login') {
    global $pdo;
    
    $stmt = $pdo->prepare("
        INSERT INTO login_attempts (ip_address, username, success, action, attempt_time) 
        VALUES (?, ?, ?, ?, NOW())
    ");
    $stmt->execute([$ip, $username, $success ? 1 : 0, $action]);
}

// Başarılı login sonrası temizlik
function clearLoginAttempts($ip, $action = 'login') {
    global $pdo;
    
    $stmt = $pdo->prepare("
        DELETE FROM login_attempts 
        WHERE ip_address = ? AND action = ? AND success = 0
    ");
    $stmt->execute([$ip, $action]);
}

// IP adresi al
function getClientIP() {
    $ipKeys = ['HTTP_CF_CONNECTING_IP', 'HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR'];
    
    foreach ($ipKeys as $key) {
        if (array_key_exists($key, $_SERVER) === true) {
            foreach (explode(',', $_SERVER[$key]) as $ip) {
                $ip = trim($ip);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                    return $ip;
                }
            }
        }
    }
    
    return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
}

// Hata loglama
function logError($message, $context = []) {
    $logMessage = date('Y-m-d H:i:s') . ' - ' . $message;
    if (!empty($context)) {
        $logMessage .= ' - Context: ' . json_encode($context);
    }
    error_log($logMessage);
}

// JSON response gönder
function sendJSONResponse($data, $httpCode = 200) {
    http_response_code($httpCode);
    header('Content-Type: application/json; charset=utf-8');
    header('X-Content-Type-Options: nosniff');
    header('X-Frame-Options: DENY');
    header('X-XSS-Protection: 1; mode=block');
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit;
}
?>
