* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: #f8fafc;
    color: #1f2937;
}

.dashboard-container {
    display: flex;
    min-height: 100vh;
}

/* Sidebar */
.sidebar {
    width: 260px;
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    color: white;
    position: fixed;
    height: 100vh;
    left: 0;
    top: 0;
    z-index: 1000;
    transition: transform 0.3s ease;
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo {
    display: flex;
    align-items: center;
    font-size: 20px;
    font-weight: 700;
}

.logo i {
    margin-right: 10px;
    font-size: 24px;
}

.sidebar-nav {
    padding: 20px 0;
}

.sidebar-nav ul {
    list-style: none;
}

.nav-item {
    margin-bottom: 5px;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover,
.nav-item.active .nav-link {
    color: white;
    background: rgba(255, 255, 255, 0.1);
}

.nav-link i {
    margin-right: 12px;
    width: 20px;
    text-align: center;
}

/* Main Content */
.main-content {
    flex: 1;
    margin-left: 260px;
    min-height: 100vh;
}

/* Header */
.header {
    background: white;
    padding: 0 30px;
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-left {
    display: flex;
    align-items: center;
}

.sidebar-toggle {
    display: none;
    background: none;
    border: none;
    font-size: 20px;
    color: #6b7280;
    cursor: pointer;
    margin-right: 15px;
}

.header h1 {
    font-size: 24px;
    font-weight: 600;
    color: #1f2937;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.notification-btn {
    position: relative;
    background: none;
    border: none;
    font-size: 20px;
    color: #6b7280;
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.notification-btn:hover {
    background: #f3f4f6;
    color: #4f46e5;
}

.notification-badge {
    position: absolute;
    top: 0;
    right: 0;
    background: #ef4444;
    color: white;
    font-size: 12px;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
}

.user-menu {
    position: relative;
}

.user-btn {
    display: flex;
    align-items: center;
    gap: 10px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.user-btn:hover {
    background: #f3f4f6;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.user-name {
    font-weight: 500;
    color: #1f2937;
}

.user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    min-width: 200px;
    padding: 8px 0;
    display: none;
    z-index: 1000;
}

.user-dropdown.show {
    display: block;
}

.dropdown-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    color: #374151;
    text-decoration: none;
    transition: all 0.3s ease;
}

.dropdown-item:hover {
    background: #f3f4f6;
    color: #4f46e5;
}

.dropdown-item i {
    margin-right: 10px;
    width: 16px;
}

.dropdown-divider {
    height: 1px;
    background: #e5e7eb;
    margin: 8px 0;
}

/* Content Sections */
.content-section {
    display: none;
}

.content-section.active {
    display: block;
}

/* Dashboard Content */
.dashboard-content {
    padding: 30px;
}

.welcome-section {
    margin-bottom: 30px;
}

.welcome-card {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    color: white;
    padding: 30px;
    border-radius: 16px;
    position: relative;
    overflow: hidden;
}

.welcome-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 200px;
    height: 200px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: translate(50%, -50%);
}

.welcome-card h2 {
    font-size: 28px;
    margin-bottom: 10px;
    position: relative;
    z-index: 1;
}

.welcome-card p {
    font-size: 16px;
    opacity: 0.9;
    margin-bottom: 20px;
    position: relative;
    z-index: 1;
}

.welcome-stats {
    display: flex;
    gap: 30px;
    position: relative;
    z-index: 1;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    opacity: 0.9;
}

.status-good {
    color: #10b981;
    font-weight: 600;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
}

.stat-card:nth-child(1) .stat-icon {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.stat-card:nth-child(2) .stat-icon {
    background: linear-gradient(135deg, #10b981, #047857);
}

.stat-card:nth-child(3) .stat-icon {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.stat-card:nth-child(4) .stat-icon {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.stat-content h3 {
    font-size: 28px;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 5px;
}

.stat-content p {
    color: #6b7280;
    font-size: 14px;
}

/* Activity Section */
.activity-section {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.section-header h3 {
    font-size: 20px;
    font-weight: 600;
    color: #1f2937;
}

.btn-secondary {
    background: #f3f4f6;
    color: #6b7280;
    border: none;
    padding: 8px 16px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: #e5e7eb;
    color: #4f46e5;
}

.activity-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.activity-item:hover {
    background: #f9fafb;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f3f4f6;
    color: #6b7280;
}

.activity-content h4 {
    font-size: 16px;
    font-weight: 500;
    color: #1f2937;
    margin-bottom: 2px;
}

.activity-content p {
    font-size: 14px;
    color: #6b7280;
    margin-bottom: 5px;
}

.activity-time {
    font-size: 12px;
    color: #9ca3af;
}

/* Alert Styles */
.alert {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    z-index: 1000;
    animation: slideIn 0.3s ease;
}

.alert.success {
    background: #10b981;
}

.alert.error {
    background: #ef4444;
}

.alert.warning {
    background: #f59e0b;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .sidebar-toggle {
        display: block;
    }
    
    .dashboard-content {
        padding: 20px;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .welcome-stats {
        flex-direction: column;
        gap: 15px;
    }
    
    .header {
        padding: 0 20px;
    }
}

/* Product Search Styles */
.search-header {
    text-align: center;
    margin-bottom: 40px;
}

.search-header h2 {
    font-size: 32px;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 10px;
}

.search-header p {
    color: #6b7280;
    font-size: 16px;
}

.search-form-container {
    background: white;
    padding: 40px;
    border-radius: 16px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    margin-bottom: 30px;
}

.search-input-group {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
}

.search-input {
    flex: 1;
    padding: 15px 20px;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 16px;
    transition: all 0.3s ease;
}

.search-input:focus {
    outline: none;
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.search-btn {
    padding: 15px 30px;
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
}

.search-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(79, 70, 229, 0.3);
}

.search-options {
    display: flex;
    gap: 30px;
    justify-content: center;
}

.search-option {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 14px;
    color: #374151;
}

.search-option input {
    margin-right: 8px;
}

/* Search Results */
.search-results {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    margin-bottom: 30px;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25px 30px;
    border-bottom: 1px solid #e5e7eb;
}

.results-header h3 {
    font-size: 20px;
    font-weight: 600;
    color: #1f2937;
}

.results-count {
    background: #f3f4f6;
    color: #6b7280;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
}

.product-list {
    padding: 20px 30px 30px;
}

.product-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
    cursor: pointer;
    background: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.product-item:hover {
    border-color: #4f46e5;
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.1);
    transform: translateY(-2px);
}

/* Enhanced Product Item */
.product-item.enhanced {
    flex-direction: column;
    align-items: stretch;
    border-left: 4px solid #4f46e5;
    padding: 25px;
}

.product-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.product-name {
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
    flex: 1;
    margin: 0;
}

.match-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    color: white;
    margin-left: 10px;
    white-space: nowrap;
}

.match-badge.exact { background: #10b981; }
.match-badge.starts { background: #3b82f6; }
.match-badge.contains { background: #8b5cf6; }
.match-badge.fuzzy { background: #f59e0b; }
.match-badge.word { background: #ef4444; }

.product-details {
    display: grid;
    gap: 12px;
    margin-bottom: 20px;
}

.detail-row {
    display: flex;
    align-items: center;
    gap: 10px;
}

.detail-label {
    font-weight: 500;
    color: #6b7280;
    min-width: 100px;
    font-size: 14px;
}

.detail-value {
    color: #1f2937;
    font-size: 14px;
    flex: 1;
}

.detail-value.clickable {
    cursor: pointer;
    color: #4f46e5;
    transition: color 0.3s ease;
    padding: 4px 8px;
    border-radius: 6px;
}

.detail-value.clickable:hover {
    color: #3730a3;
    background: #f3f4f6;
}

.detail-value.barcodes {
    font-family: 'Courier New', monospace;
    font-size: 13px;
}

.product-info {
    flex: 1;
}

.product-name {
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 5px;
}

.product-code {
    font-size: 14px;
    color: #6b7280;
    margin-bottom: 5px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.code-label {
    font-weight: 500;
}

.code-value {
    background: #f3f4f6;
    padding: 4px 8px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
    font-family: 'Courier New', monospace;
    font-weight: 600;
}

.code-value:hover {
    background: #e5e7eb;
    color: #4f46e5;
}

.copy-icon {
    font-size: 12px;
    opacity: 0.6;
    transition: opacity 0.3s ease;
}

.code-value:hover .copy-icon {
    opacity: 1;
}

.product-actions {
    display: flex;
    gap: 10px;
}

.action-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.btn-view {
    background: #3b82f6;
    color: white;
}

.btn-view:hover {
    background: #2563eb;
}

.btn-favorite {
    background: #ef4444;
    color: white;
}

.btn-favorite:hover {
    background: #dc2626;
}

.btn-favorite.active {
    background: #10b981;
}

.btn-favorite.active:hover {
    background: #059669;
}

/* Product Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25px 30px;
    border-bottom: 1px solid #e5e7eb;
}

.modal-header h3 {
    font-size: 20px;
    font-weight: 600;
    color: #1f2937;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    color: #6b7280;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.modal-close:hover {
    background: #f3f4f6;
    color: #ef4444;
}

.modal-body {
    padding: 30px;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    padding: 20px 30px;
    border-top: 1px solid #e5e7eb;
}

.product-detail {
    display: grid;
    gap: 20px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid #f3f4f6;
}

.detail-label {
    font-weight: 600;
    color: #374151;
}

.detail-value {
    color: #6b7280;
    text-align: right;
}

.detail-value.copyable {
    background: #f3f4f6;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 8px;
    font-family: 'Courier New', monospace;
    font-weight: 600;
}

.detail-value.copyable:hover {
    background: #e5e7eb;
    color: #4f46e5;
}

.barcodes-section {
    flex-direction: column;
    align-items: flex-start;
}

.barcodes-list {
    width: 100%;
    margin-top: 10px;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.barcode-item {
    background: #f8fafc;
    border: 1px solid #e5e7eb;
    padding: 12px 16px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.barcode-item:hover {
    background: #e5e7eb;
    border-color: #4f46e5;
    color: #4f46e5;
}

.barcode-value {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    font-size: 16px;
}

.no-barcode {
    color: #9ca3af;
    font-style: italic;
    padding: 20px;
    text-align: center;
    background: #f9fafb;
    border-radius: 8px;
}

/* Favorites Section */
.favorites-section {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    padding: 30px;
}

.favorites-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.favorite-item {
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
}

.favorite-item:hover {
    border-color: #4f46e5;
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.1);
}

/* Coming Soon Section */
.coming-soon {
    text-align: center;
    padding: 80px 20px;
}

.coming-soon-icon {
    width: 120px;
    height: 120px;
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 30px;
}

.coming-soon-icon i {
    font-size: 48px;
    color: white;
}

.coming-soon h2 {
    font-size: 32px;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 15px;
}

.coming-soon p {
    font-size: 18px;
    color: #6b7280;
    margin-bottom: 40px;
}

.coming-soon-features {
    display: flex;
    justify-content: center;
    gap: 40px;
    flex-wrap: wrap;
}

.coming-soon-features .feature-item {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #6b7280;
    font-size: 16px;
}

.coming-soon-features .feature-item i {
    color: #4f46e5;
    font-size: 20px;
}

/* Settings Section */
.settings-container {
    max-width: 1200px;
    margin: 0 auto;
}

.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 30px;
    margin-top: 30px;
}

.settings-card {
    background: white;
    padding: 30px;
    border-radius: 16px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.settings-card h3 {
    font-size: 20px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f3f4f6;
}

.setting-item {
    margin-bottom: 20px;
}

.setting-item label {
    display: block;
    font-weight: 500;
    color: #374151;
    margin-bottom: 8px;
}

.setting-item input {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.setting-item input:focus {
    outline: none;
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.setting-item input[type="checkbox"] {
    width: auto;
    margin-right: 8px;
}

/* Notification Settings */
.notification-settings {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.notification-group {
    background: #f9fafb;
    padding: 25px;
    border-radius: 12px;
    border: 1px solid #e5e7eb;
}

.notification-group h4 {
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e5e7eb;
}

.notification-item {
    margin-bottom: 20px;
    padding: 15px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
}

.notification-toggle {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-weight: 500;
    color: #374151;
}

.notification-toggle input[type="checkbox"] {
    display: none;
}

.toggle-slider {
    position: relative;
    width: 50px;
    height: 24px;
    background: #d1d5db;
    border-radius: 24px;
    margin-right: 12px;
    transition: all 0.3s ease;
}

.toggle-slider::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background: white;
    border-radius: 50%;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.notification-toggle input[type="checkbox"]:checked + .toggle-slider {
    background: #10b981;
}

.notification-toggle input[type="checkbox"]:checked + .toggle-slider::before {
    transform: translateX(26px);
}

.toggle-label {
    font-size: 16px;
    font-weight: 500;
}

.notification-desc {
    font-size: 14px;
    color: #6b7280;
    margin: 8px 0 0 62px;
    line-height: 1.4;
}

.notification-timing {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.timing-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.timing-item label {
    font-size: 14px;
    font-weight: 500;
    color: #374151;
}

.timing-item select {
    padding: 10px 12px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 14px;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.timing-item select:focus {
    outline: none;
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

/* Enhanced Search Info */
.search-info {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 12px;
    margin-bottom: 20px;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.search-stats {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    align-items: center;
}

.total-results {
    font-size: 18px;
    font-weight: 600;
}

.exact-matches {
    background: rgba(255, 255, 255, 0.2);
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 14px;
}

.match-details {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 10px;
}

.match-type {
    background: rgba(255, 255, 255, 0.15);
    padding: 6px 10px;
    border-radius: 15px;
    font-size: 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.match-type.starts { background: rgba(34, 197, 94, 0.2); }
.match-type.contains { background: rgba(59, 130, 246, 0.2); }
.match-type.fuzzy { background: rgba(168, 85, 247, 0.2); }
.match-type.word { background: rgba(245, 158, 11, 0.2); }

/* Enhanced Empty State */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6b7280;
}

.empty-state.enhanced {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 16px;
    border: 2px dashed #cbd5e1;
    padding: 80px 40px;
}

.empty-state.enhanced i {
    font-size: 48px;
    color: #94a3b8;
    margin-bottom: 20px;
}

.empty-state.enhanced h3 {
    color: #475569;
    margin-bottom: 10px;
    font-size: 24px;
}

.empty-state.enhanced p {
    color: #64748b;
    font-size: 16px;
    margin-bottom: 30px;
}

.search-tips {
    background: white;
    padding: 25px;
    border-radius: 12px;
    margin-top: 20px;
    text-align: left;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
}

.search-tips h4 {
    color: #1f2937;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 18px;
}

.search-tips ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.search-tips li {
    padding: 10px 0;
    color: #4b5563;
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 15px;
    border-bottom: 1px solid #f1f5f9;
}

.search-tips li:last-child {
    border-bottom: none;
}

.search-tips li i {
    color: #10b981;
    font-size: 14px;
    width: 16px;
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 20px;
    opacity: 0.5;
}

.empty-state h3 {
    font-size: 18px;
    margin-bottom: 10px;
    color: #374151;
}

.empty-state p {
    font-size: 14px;
}

/* Loading */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 40px;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f4f6;
    border-top: 4px solid #4f46e5;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Profile Styles */
.profile-container {
    max-width: 1200px;
    margin: 0 auto;
}

.profile-header {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    margin-bottom: 30px;
    overflow: hidden;
}

.profile-cover {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    padding: 40px 30px 30px;
    position: relative;
}

.profile-avatar-container {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
}

.profile-avatar {
    position: relative;
    width: 120px;
    height: 120px;
}

.profile-avatar img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    border: 4px solid white;
    object-fit: cover;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.avatar-edit-btn {
    position: absolute;
    bottom: 5px;
    right: 5px;
    width: 36px;
    height: 36px;
    background: #4f46e5;
    border: 3px solid white;
    border-radius: 50%;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    transition: all 0.3s ease;
}

.avatar-edit-btn:hover {
    background: #3730a3;
    transform: scale(1.1);
}

.profile-info {
    text-align: center;
    color: white;
}

.profile-name {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 5px;
}

.profile-email {
    font-size: 16px;
    opacity: 0.9;
    margin-bottom: 25px;
}

.profile-stats {
    display: flex;
    justify-content: center;
    gap: 40px;
}

.profile-stats .stat-item {
    text-align: center;
}

.profile-stats .stat-number {
    display: block;
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 5px;
}

.profile-stats .stat-label {
    font-size: 14px;
    opacity: 0.9;
}

.profile-content {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.profile-tabs {
    display: flex;
    border-bottom: 1px solid #e5e7eb;
}

.tab-btn {
    flex: 1;
    padding: 20px;
    background: none;
    border: none;
    font-size: 16px;
    font-weight: 500;
    color: #6b7280;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    border-bottom: 3px solid transparent;
}

.tab-btn:hover {
    background: #f9fafb;
    color: #4f46e5;
}

.tab-btn.active {
    color: #4f46e5;
    border-bottom-color: #4f46e5;
    background: #f9fafb;
}

.tab-content {
    display: none;
    padding: 30px;
}

.tab-content.active {
    display: block;
}

.profile-form h3 {
    font-size: 24px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f3f4f6;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-group label {
    font-weight: 500;
    color: #374151;
    margin-bottom: 8px;
    font-size: 14px;
}

.form-group input,
.form-group textarea {
    padding: 12px 15px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
    font-family: inherit;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    padding-top: 20px;
    border-top: 1px solid #e5e7eb;
}

/* Security Section */
.security-section {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.security-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.security-item:hover {
    border-color: #4f46e5;
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.1);
}

.security-info h4 {
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 5px;
}

.security-info p {
    font-size: 14px;
    color: #6b7280;
    margin: 0;
}

.password-form {
    margin-top: 30px;
    padding: 25px;
    background: #f9fafb;
    border-radius: 12px;
    border: 1px solid #e5e7eb;
}

.password-form h4 {
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 20px;
}

/* Activity List */
.activity-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.activity-item {
    display: flex;
    align-items: center;
    padding: 15px;
    background: #f9fafb;
    border-radius: 8px;
    border-left: 4px solid #4f46e5;
}

.activity-icon {
    width: 40px;
    height: 40px;
    background: #4f46e5;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-right: 15px;
    font-size: 16px;
}

.activity-content h4 {
    font-size: 14px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 3px;
}

.activity-content p {
    font-size: 13px;
    color: #6b7280;
    margin-bottom: 3px;
}

.activity-time {
    font-size: 12px;
    color: #9ca3af;
}

/* Responsive */
@media (max-width: 768px) {
    .profile-stats {
        gap: 20px;
    }

    .profile-stats .stat-number {
        font-size: 20px;
    }

    .form-grid {
        grid-template-columns: 1fr;
    }

    .security-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .profile-tabs {
        flex-direction: column;
    }

    .tab-btn {
        justify-content: flex-start;
        padding: 15px 20px;
    }

    /* Bildirim ayarları mobil */
    .notification-settings {
        gap: 20px;
    }

    .notification-group {
        padding: 20px 15px;
    }

    .notification-item {
        padding: 12px;
    }

    .notification-desc {
        margin-left: 0;
        margin-top: 8px;
    }

    .notification-timing {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .toggle-slider {
        width: 44px;
        height: 22px;
    }

    .toggle-slider::before {
        width: 18px;
        height: 18px;
    }

    .notification-toggle input[type="checkbox"]:checked + .toggle-slider::before {
        transform: translateX(22px);
    }
}

/* OTP Management Styles */
.otp-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 15px;
    border-left: 4px solid #e5e7eb;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.stat-card.active {
    border-left-color: #10b981;
}

.stat-card.used {
    border-left-color: #3b82f6;
}

.stat-card.expired {
    border-left-color: #ef4444;
}

.stat-card.total {
    border-left-color: #8b5cf6;
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: white;
}

.stat-card.active .stat-icon {
    background: #10b981;
}

.stat-card.used .stat-icon {
    background: #3b82f6;
}

.stat-card.expired .stat-icon {
    background: #ef4444;
}

.stat-card.total .stat-icon {
    background: #8b5cf6;
}

.stat-info h3 {
    font-size: 28px;
    font-weight: 700;
    color: #1f2937;
    margin: 0 0 5px 0;
}

.stat-info p {
    font-size: 14px;
    color: #6b7280;
    margin: 0;
}

.otp-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.otp-header {
    background: #f8fafc;
    padding: 20px 25px;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.otp-header h3 {
    margin: 0;
    color: #1f2937;
    font-size: 18px;
}

.otp-filters select {
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    background: white;
    color: #374151;
    cursor: pointer;
}

.otp-list {
    max-height: 600px;
    overflow-y: auto;
}

.otp-item {
    padding: 20px 25px;
    border-bottom: 1px solid #f1f5f9;
    transition: background-color 0.3s ease;
}

.otp-item:hover {
    background: #f8fafc;
}

.otp-item:last-child {
    border-bottom: none;
}

.otp-item.active {
    border-left: 4px solid #10b981;
}

.otp-item.used {
    border-left: 4px solid #3b82f6;
    opacity: 0.8;
}

.otp-item.expired {
    border-left: 4px solid #ef4444;
    opacity: 0.7;
}

.otp-item .otp-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 0;
    background: none;
    border: none;
}

.otp-user {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    color: #1f2937;
}

.otp-status {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 5px;
}

.otp-status.active {
    background: #d1fae5;
    color: #065f46;
}

.otp-status.used {
    background: #dbeafe;
    color: #1e40af;
}

.otp-status.expired {
    background: #fee2e2;
    color: #991b1b;
}

/* Admin Only Elements */
.admin-only {
    display: none;
}

body.admin .admin-only {
    display: block;
}

/* Dashboard Mobile Responsive */
@media (max-width: 768px) {
    .header {
        padding: 0 15px;
    }

    .header-left h1 {
        font-size: 18px;
    }

    .user-btn {
        padding: 8px 12px;
        font-size: 14px;
    }

    .user-avatar {
        width: 32px;
        height: 32px;
    }

    .sidebar {
        width: 280px;
    }

    .main-content {
        margin-left: 0;
    }

    .dashboard-content {
        padding: 20px 15px;
    }

    /* Search Form Mobile */
    .search-form-container {
        padding: 20px 15px;
    }

    .search-input-group {
        flex-direction: column;
        gap: 10px;
    }

    .search-btn {
        width: 100%;
        justify-content: center;
    }

    .search-options {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }

    /* Product Items Mobile */
    .product-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .product-actions {
        width: 100%;
        justify-content: space-between;
    }

    .action-btn {
        flex: 1;
        justify-content: center;
        margin: 0 5px;
    }

    /* Modal Mobile */
    .modal-content {
        width: 95%;
        margin: 20px;
        max-height: calc(100vh - 40px);
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 20px 15px;
    }

    .modal-footer {
        flex-direction: column;
        gap: 10px;
    }

    .modal-footer button {
        width: 100%;
    }

    /* Favorites Mobile */
    .favorites-list {
        grid-template-columns: 1fr;
    }

    /* Settings Mobile */
    .settings-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .settings-card {
        padding: 20px 15px;
    }

    .form-actions {
        flex-direction: column;
        gap: 10px;
    }

    .form-actions button {
        width: 100%;
    }

    /* Admin Panel Mobile */
    .admin-stats {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    .admin-content {
        padding: 15px;
    }

    .admin-table {
        font-size: 14px;
    }

    .admin-table th,
    .admin-table td {
        padding: 8px 4px;
    }

    /* Expiry Tracking Mobile */
    .expiry-form-container {
        padding: 20px 15px;
    }

    .expiry-input-group {
        flex-direction: column;
        gap: 10px;
    }

    .expiry-list {
        grid-template-columns: 1fr;
    }

    .expiry-item {
        padding: 15px;
    }

    .expiry-actions {
        flex-direction: column;
        gap: 8px;
        margin-top: 10px;
    }

    .expiry-actions button {
        width: 100%;
        font-size: 12px;
        padding: 8px 12px;
    }
}

/* Expiry Tracking Styles */
.expiry-header {
    text-align: center;
    margin-bottom: 40px;
}

.expiry-header h2 {
    font-size: 32px;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 10px;
}

.expiry-header p {
    color: #6b7280;
    font-size: 16px;
}

.expiry-form-container {
    background: white;
    padding: 30px;
    border-radius: 16px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    margin-bottom: 30px;
}

.expiry-form h3 {
    font-size: 20px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 20px;
}

.expiry-input-group {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.input-row {
    display: flex;
    gap: 15px;
    align-items: flex-end;
}

.input-row.full-width {
    flex-direction: column;
    gap: 10px;
}

.input-with-search {
    position: relative;
    flex: 1;
    min-width: 200px;
}

.expiry-input {
    flex: 1;
    min-width: 150px;
    padding: 12px 15px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
    width: 100%;
    box-sizing: border-box;
}

.expiry-input:focus {
    outline: none;
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.expiry-input:read-only {
    background-color: #f9fafb;
    color: #6b7280;
}

.expiry-textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 14px;
    font-family: inherit;
    resize: vertical;
    min-height: 60px;
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.expiry-textarea:focus {
    outline: none;
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.image-input-group {
    display: flex;
    gap: 10px;
    align-items: center;
}

.image-url-input {
    flex: 1;
}

.preview-btn {
    background: #6b7280;
    color: white;
    border: none;
    padding: 12px 15px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.preview-btn:hover {
    background: #4b5563;
}

.search-product-btn {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    background: #4f46e5;
    color: white;
    border: none;
    padding: 8px 10px;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.search-product-btn:hover {
    background: #3730a3;
}

.image-preview {
    position: relative;
    max-width: 200px;
    margin: 10px 0;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border: 2px solid #e5e7eb;
}

.image-preview img {
    width: 100%;
    height: 150px;
    object-fit: cover;
    display: block;
}

.image-overlay {
    position: absolute;
    top: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 0 0 0 12px;
}

.remove-image-btn {
    background: #ef4444;
    color: white;
    border: none;
    padding: 8px 10px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    border-radius: 0 0 0 8px;
}

.remove-image-btn:hover {
    background: #dc2626;
}

.form-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 10px;
}

.expiry-btn {
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    white-space: nowrap;
}

.expiry-btn.primary {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
}

.expiry-btn.primary:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.expiry-btn.secondary {
    background: #6b7280;
    color: white;
}

.expiry-btn.secondary:hover {
    background: #4b5563;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(107, 114, 128, 0.3);
}

.expiry-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(16, 185, 129, 0.3);
}

/* Filters */
.expiry-filters {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
    background: white;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.filter-group label {
    font-weight: 500;
    color: #374151;
    font-size: 14px;
}

.filter-select {
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    background: white;
    cursor: pointer;
}

/* Expiry Results */
.expiry-results {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    margin-bottom: 30px;
}

.expiry-list {
    padding: 20px;
    display: grid;
    gap: 15px;
}

.expiry-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    border-radius: 12px;
    transition: all 0.3s ease;
    position: relative;
}

.expiry-item.critical {
    background: #fef2f2;
    border-left: 4px solid #ef4444;
}

.expiry-item.warning {
    background: #fffbeb;
    border-left: 4px solid #f59e0b;
}

.expiry-item.normal {
    background: #f0fdf4;
    border-left: 4px solid #10b981;
}

.expiry-item.expired {
    background: #fafafa;
    border-left: 4px solid #6b7280;
    opacity: 0.8;
}

/* Expiry Image */
.expiry-image {
    flex-shrink: 0;
    width: 80px;
    height: 80px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-right: 15px;
}

.expiry-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.expiry-image:hover img {
    transform: scale(1.05);
}

.expiry-info {
    flex: 1;
}

.expiry-info.with-image {
    margin-left: 0;
}

.expiry-product-name {
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 5px;
}

.expiry-product-code {
    font-size: 14px;
    color: #6b7280;
    margin-bottom: 5px;
    font-family: 'Courier New', monospace;
}

.expiry-notes {
    font-size: 14px;
    color: #6b7280;
    font-style: italic;
    padding: 8px 12px;
    background: #f8fafc;
    border-radius: 6px;
    border-left: 3px solid #e2e8f0;
    margin-top: 8px;
}

.expiry-notes i {
    color: #94a3b8;
    margin-right: 5px;
}

.expiry-date-info {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 14px;
}

.expiry-date {
    font-weight: 600;
}

.expiry-days {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

.expiry-item.critical .expiry-days {
    background: #ef4444;
    color: white;
}

.expiry-item.warning .expiry-days {
    background: #f59e0b;
    color: white;
}

.expiry-item.normal .expiry-days {
    background: #10b981;
    color: white;
}

.expiry-item.expired .expiry-days {
    background: #6b7280;
    color: white;
}

.expiry-quantity {
    font-size: 14px;
    color: #6b7280;
    margin-left: 15px;
}

.expiry-actions {
    display: flex;
    gap: 8px;
}

.expiry-action-btn {
    padding: 6px 12px;
    border: none;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 4px;
}

.btn-share {
    background: #3b82f6;
    color: white;
}

.btn-share:hover {
    background: #2563eb;
}

.btn-edit {
    background: #f59e0b;
    color: white;
}

.btn-edit:hover {
    background: #d97706;
}

.btn-delete {
    background: #ef4444;
    color: white;
}

.btn-delete:hover {
    background: #dc2626;
}

/* Statistics Cards */
.expiry-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 30px;
}

.expiry-stats .stat-card {
    background: white;
    padding: 25px;
    border-radius: 16px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: all 0.3s ease;
}

.expiry-stats .stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.expiry-stats .stat-card.critical {
    border-left: 4px solid #ef4444;
}

.expiry-stats .stat-card.warning {
    border-left: 4px solid #f59e0b;
}

.expiry-stats .stat-card.expired {
    border-left: 4px solid #6b7280;
}

.expiry-stats .stat-card.total {
    border-left: 4px solid #4f46e5;
}

.expiry-stats .stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
}

.expiry-stats .stat-card.critical .stat-icon {
    background: #fef2f2;
    color: #ef4444;
}

.expiry-stats .stat-card.warning .stat-icon {
    background: #fffbeb;
    color: #f59e0b;
}

.expiry-stats .stat-card.expired .stat-icon {
    background: #f9fafb;
    color: #6b7280;
}

.expiry-stats .stat-card.total .stat-icon {
    background: #eef2ff;
    color: #4f46e5;
}

.expiry-stats .stat-content h3 {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 5px;
}

.expiry-stats .stat-content p {
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 3px;
}

.expiry-stats .stat-desc {
    font-size: 12px;
    color: #6b7280;
}

/* Alert Messages */
.expiry-alert {
    padding: 15px 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 500;
}

.expiry-alert.critical {
    background: #fef2f2;
    color: #991b1b;
    border: 1px solid #fecaca;
}

.expiry-alert.warning {
    background: #fffbeb;
    color: #92400e;
    border: 1px solid #fed7aa;
}

.expiry-alert i {
    font-size: 18px;
}
