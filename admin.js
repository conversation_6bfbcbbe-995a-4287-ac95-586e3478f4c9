// Admin Panel JavaScript

// Global variables
let currentPage = 1;
let usersPerPage = 10;
let currentSection = 'dashboard';

// <PERSON><PERSON> yüklendiğinde çalışacak fonksiyonlar
document.addEventListener('DOMContentLoaded', function() {
    checkAdminAuthentication();
    initializeAdmin();
    loadDashboardData();
});

// Admin kimlik doğrulaması kontrolü
async function checkAdminAuthentication() {
    try {
        const response = await fetch('check_admin_session.php');
        const data = await response.json();
        
        if (!data.authenticated || data.user.role !== 'admin') {
            showAlert('Admin yetkisi gerekli!', 'error');
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 2000);
            return;
        }
        
        // Admin bilgilerini güncelle
        updateAdminInfo(data.user);
        
    } catch (error) {
        console.error('Admin authentication check failed:', error);
        showAlert('<PERSON><PERSON> doğrulam<PERSON> hatası!', 'error');
        setTimeout(() => {
            window.location.href = 'index.html';
        }, 2000);
    }
}

// Admin bilgilerini güncelle
function updateAdminInfo(admin) {
    const adminName = document.getElementById('adminName');
    if (adminName) adminName.textContent = admin.username || 'Admin';
}

// Admin panelini başlat
function initializeAdmin() {
    // Sidebar navigation
    const navLinks = document.querySelectorAll('.nav-link[data-section]');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const section = this.getAttribute('data-section');
            showSection(section);
        });
    });
    
    // Sidebar toggle (mobile)
    const sidebarToggle = document.querySelector('.sidebar-toggle');
    const sidebar = document.querySelector('.sidebar');
    
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('show');
        });
    }
    
    // Modal events
    const addUserForm = document.getElementById('addUserForm');
    if (addUserForm) {
        addUserForm.addEventListener('submit', handleAddUser);
    }
    
    // Filter events
    const userSearch = document.getElementById('userSearch');
    if (userSearch) {
        userSearch.addEventListener('input', debounce(filterUsers, 300));
    }
    
    const roleFilter = document.getElementById('roleFilter');
    if (roleFilter) {
        roleFilter.addEventListener('change', filterUsers);
    }
    
    const statusFilter = document.getElementById('statusFilter');
    if (statusFilter) {
        statusFilter.addEventListener('change', filterUsers);
    }
}

// Bölüm göster
function showSection(sectionName) {
    // Tüm bölümleri gizle
    const sections = document.querySelectorAll('.content-section');
    sections.forEach(section => {
        section.classList.remove('active');
    });
    
    // Seçilen bölümü göster
    const targetSection = document.getElementById(sectionName + '-section');
    if (targetSection) {
        targetSection.classList.add('active');
    }
    
    // Sidebar navigation güncelle
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach(item => {
        item.classList.remove('active');
    });
    
    const activeNavItem = document.querySelector(`[data-section="${sectionName}"]`);
    if (activeNavItem) {
        activeNavItem.closest('.nav-item').classList.add('active');
    }
    
    // Sayfa başlığını güncelle
    const pageTitle = document.getElementById('pageTitle');
    const titles = {
        'dashboard': 'Admin Dashboard',
        'users': 'Kullanıcı Yönetimi',
        'logs': 'Sistem Logları',
        'security': 'Güvenlik Raporu',
        'settings': 'Sistem Ayarları'
    };
    
    if (pageTitle && titles[sectionName]) {
        pageTitle.textContent = titles[sectionName];
    }
    
    currentSection = sectionName;
    
    // Bölüme özel verileri yükle
    switch(sectionName) {
        case 'dashboard':
            loadDashboardData();
            break;
        case 'users':
            loadUsers();
            break;
        case 'logs':
            loadLogs();
            break;
        case 'security':
            loadSecurityData();
            break;
        case 'settings':
            loadSettings();
            break;
    }
}

// Dashboard verilerini yükle
async function loadDashboardData() {
    try {
        const response = await fetch('admin_api.php?action=dashboard_stats');
        const data = await response.json();
        
        if (data.success) {
            // İstatistikleri güncelle
            document.getElementById('totalUsers').textContent = data.stats.total_users || '0';
            document.getElementById('activeUsers').textContent = data.stats.active_users || '0';
            document.getElementById('todayLogins').textContent = data.stats.today_logins || '0';
            document.getElementById('failedAttempts').textContent = data.stats.failed_attempts || '0';
            
            // Son aktiviteleri yükle
            loadRecentActivity();
        }
    } catch (error) {
        console.error('Dashboard data load error:', error);
        showAlert('Dashboard verileri yüklenirken hata oluştu!', 'error');
    }
}

// Son aktiviteleri yükle
async function loadRecentActivity() {
    try {
        const response = await fetch('admin_api.php?action=recent_activity');
        const data = await response.json();
        
        if (data.success) {
            const activityContainer = document.getElementById('recentActivity');
            if (activityContainer) {
                activityContainer.innerHTML = '';
                
                if (data.activities.length === 0) {
                    activityContainer.innerHTML = '<div class="empty-state"><i class="fas fa-inbox"></i><h3>Henüz aktivite yok</h3><p>Son aktiviteler burada görünecek</p></div>';
                    return;
                }
                
                data.activities.forEach(activity => {
                    const activityItem = createActivityItem(activity);
                    activityContainer.appendChild(activityItem);
                });
            }
        }
    } catch (error) {
        console.error('Recent activity load error:', error);
    }
}

// Aktivite öğesi oluştur
function createActivityItem(activity) {
    const div = document.createElement('div');
    div.className = 'activity-item';
    
    const iconClass = getActivityIcon(activity.action);
    const timeAgo = getTimeAgo(activity.created_at);
    
    div.innerHTML = `
        <div class="activity-icon">
            <i class="${iconClass}"></i>
        </div>
        <div class="activity-content">
            <h4>${activity.action}</h4>
            <p>${activity.description}</p>
            <span class="activity-time">${timeAgo}</span>
        </div>
    `;
    
    return div;
}

// Aktivite ikonu al
function getActivityIcon(action) {
    const icons = {
        'login': 'fas fa-sign-in-alt',
        'logout': 'fas fa-sign-out-alt',
        'user_created': 'fas fa-user-plus',
        'user_updated': 'fas fa-user-edit',
        'user_deleted': 'fas fa-user-minus',
        'settings_updated': 'fas fa-cog'
    };
    
    return icons[action] || 'fas fa-info-circle';
}

// Zaman farkını hesapla
function getTimeAgo(dateString) {
    const now = new Date();
    const date = new Date(dateString);
    const diffInSeconds = Math.floor((now - date) / 1000);
    
    if (diffInSeconds < 60) {
        return 'Az önce';
    } else if (diffInSeconds < 3600) {
        const minutes = Math.floor(diffInSeconds / 60);
        return `${minutes} dakika önce`;
    } else if (diffInSeconds < 86400) {
        const hours = Math.floor(diffInSeconds / 3600);
        return `${hours} saat önce`;
    } else {
        const days = Math.floor(diffInSeconds / 86400);
        return `${days} gün önce`;
    }
}

// Kullanıcıları yükle
async function loadUsers(page = 1) {
    try {
        showLoading('usersTableBody');
        
        const searchTerm = document.getElementById('userSearch')?.value || '';
        const roleFilter = document.getElementById('roleFilter')?.value || '';
        const statusFilter = document.getElementById('statusFilter')?.value || '';
        
        const params = new URLSearchParams({
            action: 'get_users',
            page: page,
            limit: usersPerPage,
            search: searchTerm,
            role: roleFilter,
            status: statusFilter
        });
        
        const response = await fetch(`admin_api.php?${params}`);
        const data = await response.json();
        
        if (data.success) {
            displayUsers(data.users);
            displayUsersPagination(data.pagination);
        } else {
            showAlert(data.message || 'Kullanıcılar yüklenirken hata oluştu!', 'error');
        }
    } catch (error) {
        console.error('Users load error:', error);
        showAlert('Kullanıcılar yüklenirken hata oluştu!', 'error');
    }
}

// Kullanıcıları göster
function displayUsers(users) {
    const tbody = document.getElementById('usersTableBody');
    if (!tbody) return;
    
    tbody.innerHTML = '';
    
    if (users.length === 0) {
        tbody.innerHTML = '<tr><td colspan="8" class="empty-state"><i class="fas fa-users"></i><h3>Kullanıcı bulunamadı</h3><p>Arama kriterlerinizi değiştirmeyi deneyin</p></td></tr>';
        return;
    }
    
    users.forEach(user => {
        const row = createUserRow(user);
        tbody.appendChild(row);
    });
}

// Kullanıcı satırı oluştur
function createUserRow(user) {
    const tr = document.createElement('tr');
    
    const statusClass = user.is_active ? 'status-active' : 'status-inactive';
    const statusText = user.is_active ? 'Aktif' : 'Pasif';
    
    const roleClass = `role-${user.role}`;
    const roleText = {
        'admin': 'Admin',
        'user': 'Kullanıcı',
        'moderator': 'Moderatör'
    }[user.role] || user.role;
    
    const lastLogin = user.last_login ? new Date(user.last_login).toLocaleString('tr-TR') : 'Hiç';
    
    tr.innerHTML = `
        <td>${user.id}</td>
        <td>${user.username}</td>
        <td>${user.email}</td>
        <td>${user.first_name || ''} ${user.last_name || ''}</td>
        <td><span class="role-badge ${roleClass}">${roleText}</span></td>
        <td><span class="status-badge ${statusClass}">${statusText}</span></td>
        <td>${lastLogin}</td>
        <td>
            <div class="table-actions">
                <button class="action-btn-small btn-view" onclick="viewUser(${user.id})" title="Görüntüle">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="action-btn-small btn-edit" onclick="editUser(${user.id})" title="Düzenle">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="action-btn-small btn-delete" onclick="deleteUser(${user.id})" title="Sil">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </td>
    `;
    
    return tr;
}

// Kullanıcı pagination göster
function displayUsersPagination(pagination) {
    const container = document.getElementById('usersPagination');
    if (!container) return;
    
    container.innerHTML = '';
    
    if (pagination.total_pages <= 1) return;
    
    // Previous button
    const prevBtn = document.createElement('button');
    prevBtn.textContent = 'Önceki';
    prevBtn.disabled = pagination.current_page === 1;
    prevBtn.onclick = () => loadUsers(pagination.current_page - 1);
    container.appendChild(prevBtn);
    
    // Page numbers
    for (let i = 1; i <= pagination.total_pages; i++) {
        const pageBtn = document.createElement('button');
        pageBtn.textContent = i;
        pageBtn.className = i === pagination.current_page ? 'active' : '';
        pageBtn.onclick = () => loadUsers(i);
        container.appendChild(pageBtn);
    }
    
    // Next button
    const nextBtn = document.createElement('button');
    nextBtn.textContent = 'Sonraki';
    nextBtn.disabled = pagination.current_page === pagination.total_pages;
    nextBtn.onclick = () => loadUsers(pagination.current_page + 1);
    container.appendChild(nextBtn);
}

// Kullanıcı filtrele
function filterUsers() {
    currentPage = 1;
    loadUsers(currentPage);
}

// Yeni kullanıcı modal göster
function showAddUserModal() {
    const modal = document.getElementById('addUserModal');
    if (modal) {
        modal.classList.add('show');
        document.getElementById('newUsername').focus();
    }
}

// Yeni kullanıcı modal kapat
function closeAddUserModal() {
    const modal = document.getElementById('addUserModal');
    if (modal) {
        modal.classList.remove('show');
        document.getElementById('addUserForm').reset();
    }
}

// Yeni kullanıcı ekle
async function handleAddUser(e) {
    e.preventDefault();
    
    const formData = {
        username: document.getElementById('newUsername').value,
        email: document.getElementById('newEmail').value,
        first_name: document.getElementById('newFirstName').value,
        last_name: document.getElementById('newLastName').value,
        password: document.getElementById('newPassword').value,
        role: document.getElementById('newRole').value,
        phone: document.getElementById('newPhone').value,
        is_active: document.getElementById('newIsActive').checked
    };
    
    try {
        const response = await fetch('admin_api.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'create_user',
                ...formData
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            showAlert('Kullanıcı başarıyla oluşturuldu!', 'success');
            closeAddUserModal();
            loadUsers(currentPage);
        } else {
            showAlert(data.message || 'Kullanıcı oluşturulurken hata oluştu!', 'error');
        }
    } catch (error) {
        console.error('Create user error:', error);
        showAlert('Kullanıcı oluşturulurken hata oluştu!', 'error');
    }
}

// Kullanıcı görüntüle
function viewUser(userId) {
    showAlert('Kullanıcı detay özelliği yakında aktif olacak!', 'info');
}

// Kullanıcı düzenle
function editUser(userId) {
    showAlert('Kullanıcı düzenleme özelliği yakında aktif olacak!', 'info');
}

// Kullanıcı sil
async function deleteUser(userId) {
    if (!confirm('Bu kullanıcıyı silmek istediğinizden emin misiniz?')) {
        return;
    }
    
    try {
        const response = await fetch('admin_api.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'delete_user',
                user_id: userId
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            showAlert('Kullanıcı başarıyla silindi!', 'success');
            loadUsers(currentPage);
        } else {
            showAlert(data.message || 'Kullanıcı silinirken hata oluştu!', 'error');
        }
    } catch (error) {
        console.error('Delete user error:', error);
        showAlert('Kullanıcı silinirken hata oluştu!', 'error');
    }
}

// Logları yükle
async function loadLogs() {
    try {
        showLoading('logsTableBody');
        
        const response = await fetch('admin_api.php?action=get_logs');
        const data = await response.json();
        
        if (data.success) {
            displayLogs(data.logs);
        } else {
            showAlert(data.message || 'Loglar yüklenirken hata oluştu!', 'error');
        }
    } catch (error) {
        console.error('Logs load error:', error);
        showAlert('Loglar yüklenirken hata oluştu!', 'error');
    }
}

// Logları göster
function displayLogs(logs) {
    const tbody = document.getElementById('logsTableBody');
    if (!tbody) return;
    
    tbody.innerHTML = '';
    
    if (logs.length === 0) {
        tbody.innerHTML = '<tr><td colspan="5" class="empty-state"><i class="fas fa-list-alt"></i><h3>Log bulunamadı</h3><p>Henüz sistem logu bulunmuyor</p></td></tr>';
        return;
    }
    
    logs.forEach(log => {
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${new Date(log.created_at).toLocaleString('tr-TR')}</td>
            <td>${log.username || 'Sistem'}</td>
            <td>${log.action}</td>
            <td>${log.description}</td>
            <td>${log.ip_address}</td>
        `;
        tbody.appendChild(tr);
    });
}

// Güvenlik verilerini yükle
async function loadSecurityData() {
    try {
        const response = await fetch('admin_api.php?action=security_stats');
        const data = await response.json();
        
        if (data.success) {
            document.getElementById('securityFailedLogins').textContent = data.stats.failed_logins || '0';
            document.getElementById('securityLockedAccounts').textContent = data.stats.locked_accounts || '0';
            document.getElementById('securitySuspiciousIPs').textContent = data.stats.suspicious_ips || '0';
            
            displayFailedAttempts(data.failed_attempts || []);
        }
    } catch (error) {
        console.error('Security data load error:', error);
        showAlert('Güvenlik verileri yüklenirken hata oluştu!', 'error');
    }
}

// Başarısız denemeleri göster
function displayFailedAttempts(attempts) {
    const tbody = document.getElementById('failedAttemptsTableBody');
    if (!tbody) return;
    
    tbody.innerHTML = '';
    
    if (attempts.length === 0) {
        tbody.innerHTML = '<tr><td colspan="5" class="empty-state"><i class="fas fa-shield-alt"></i><h3>Başarısız deneme yok</h3><p>Son zamanlarda başarısız giriş denemesi bulunmuyor</p></td></tr>';
        return;
    }
    
    attempts.forEach(attempt => {
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${new Date(attempt.attempt_time).toLocaleString('tr-TR')}</td>
            <td>${attempt.ip_address}</td>
            <td>${attempt.username || 'Bilinmiyor'}</td>
            <td>${attempt.attempt_count}</td>
            <td>
                <button class="action-btn-small btn-delete" onclick="blockIP('${attempt.ip_address}')" title="IP'yi Engelle">
                    <i class="fas fa-ban"></i>
                </button>
            </td>
        `;
        tbody.appendChild(tr);
    });
}

// IP engelle
function blockIP(ipAddress) {
    showAlert('IP engelleme özelliği yakında aktif olacak!', 'info');
}

// Ayarları yükle
function loadSettings() {
    // Mevcut ayarları yükle (demo)
    showAlert('Ayarlar yüklendi', 'info');
}

// Ayarları kaydet
function saveSettings() {
    showAlert('Ayarlar kaydedildi', 'success');
}

// Eski logları temizle
async function clearOldLogs() {
    if (!confirm('Eski logları temizlemek istediğinizden emin misiniz?')) {
        return;
    }
    
    try {
        const response = await fetch('admin_api.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'clear_old_logs'
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            showAlert('Eski loglar temizlendi!', 'success');
            loadLogs();
        } else {
            showAlert(data.message || 'Loglar temizlenirken hata oluştu!', 'error');
        }
    } catch (error) {
        console.error('Clear logs error:', error);
        showAlert('Loglar temizlenirken hata oluştu!', 'error');
    }
}

// Güvenlik verilerini yenile
function refreshSecurityData() {
    loadSecurityData();
    showAlert('Güvenlik verileri yenilendi', 'info');
}

// Veri dışa aktar
function exportData() {
    showAlert('Veri dışa aktarma özelliği yakında aktif olacak!', 'info');
}

// Kullanıcı menüsünü aç/kapat
function toggleUserMenu() {
    const dropdown = document.getElementById('userDropdown');
    dropdown.classList.toggle('show');
}

// Çıkış yap
async function logout() {
    try {
        const response = await fetch('logout.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });
        
        const data = await response.json();
        
        if (data.success) {
            showAlert('Başarıyla çıkış yaptınız!', 'success');
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 1500);
        }
    } catch (error) {
        console.error('Logout error:', error);
        setTimeout(() => {
            window.location.href = 'index.html';
        }, 2000);
    }
}

// Yardımcı fonksiyonlar

// Loading göster
function showLoading(elementId) {
    const element = document.getElementById(elementId);
    if (element) {
        element.innerHTML = '<tr><td colspan="8" style="text-align: center; padding: 40px;"><div class="loading"></div></td></tr>';
    }
}

// Alert mesajı göster
function showAlert(message, type = 'info') {
    const alertContainer = document.getElementById('alertContainer');
    
    const alert = document.createElement('div');
    alert.className = `alert ${type}`;
    alert.textContent = message;
    
    alertContainer.appendChild(alert);
    
    setTimeout(() => {
        alert.remove();
    }, 5000);
}

// Debounce fonksiyonu
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Modal dışına tıklandığında kapat
document.addEventListener('click', function(e) {
    const modal = document.getElementById('addUserModal');
    if (e.target === modal) {
        closeAddUserModal();
    }
    
    const userMenu = document.querySelector('.user-menu');
    const dropdown = document.getElementById('userDropdown');
    
    if (!userMenu.contains(e.target)) {
        dropdown.classList.remove('show');
    }
});

// Klavye kısayolları
document.addEventListener('keydown', function(e) {
    // Escape: Modal'ları kapat
    if (e.key === 'Escape') {
        closeAddUserModal();
        
        const dropdown = document.getElementById('userDropdown');
        dropdown.classList.remove('show');
    }
    
    // Ctrl + N: Yeni kullanıcı
    if (e.ctrlKey && e.key === 'n' && currentSection === 'users') {
        e.preventDefault();
        showAddUserModal();
    }
});

// Responsive sidebar
window.addEventListener('resize', function() {
    const sidebar = document.querySelector('.sidebar');
    
    if (window.innerWidth > 1024) {
        sidebar.classList.remove('show');
    }
});

// Sidebar dışına tıklandığında kapat (mobil)
document.addEventListener('click', function(e) {
    const sidebar = document.querySelector('.sidebar');
    const sidebarToggle = document.querySelector('.sidebar-toggle');
    
    if (window.innerWidth <= 1024) {
        if (!sidebar.contains(e.target) && !sidebarToggle.contains(e.target)) {
            sidebar.classList.remove('show');
        }
    }
});
