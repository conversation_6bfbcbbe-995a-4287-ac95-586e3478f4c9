# Profesyonel Login Sistemi

Modern, gü<PERSON>li ve responsive bir login sistemi. HTML5, CSS3, JavaScript ve PHP ile geliştirilmiştir.

## 🚀 Özellikler

### 🎨 Tasarım
- **Modern ve Profesyonel**: Gradient renkler ve smooth animasyonlar
- **Responsive**: Tüm cihazlarda mükemmel görünüm
- **Kullanıcı Dostu**: Sezgisel arayüz ve kolay navigasyon
- **Accessibility**: Erişilebilirlik standartlarına uygun

### 🔒 Güvenlik
- **Argon2ID Şifreleme**: En güvenli password hashing algoritması
- **Rate Limiting**: Brute force saldırılarına karşı koruma
- **Session Güvenliği**: Hijacking ve fixation koruması
- **CSRF Koruması**: Cross-site request forgery önleme
- **SQL Injection Koruması**: Prepared statements kullanımı
- **XSS Koruması**: Input sanitization ve output encoding

### 💾 Veritabanı
- **MySQL/MariaDB**: Optimize edilmiş tablo yapısı
- **Indexleme**: Hızlı sorgular için optimize edilmiş indexler
- **Otomatik Temizlik**: Eski kayıtların otomatik silinmesi
- **Backup Friendly**: Kolay yedekleme ve geri yükleme

### 🔧 Özellikler
- **Remember Me**: Güvenli otomatik giriş
- **Session Management**: Gelişmiş oturum yönetimi
- **Login Attempts**: Başarısız deneme takibi
- **User Lockout**: Hesap kilitleme sistemi
- **Activity Logging**: Detaylı aktivite kayıtları

## 📋 Gereksinimler

- **PHP**: 7.4 veya üzeri (8.0+ önerilir)
- **MySQL**: 5.7 veya üzeri / MariaDB 10.2+
- **Web Server**: Apache/Nginx
- **PHP Extensions**:
  - PDO
  - PDO_MySQL
  - OpenSSL
  - JSON
  - Session

## 🛠️ Kurulum

### 1. Dosyaları Yükleyin
```bash
# Projeyi web sunucunuzun root dizinine kopyalayın
cp -r * /var/www/html/
```

### 2. Veritabanını Oluşturun
```sql
-- MySQL/MariaDB'ye bağlanın ve database.sql dosyasını çalıştırın
mysql -u root -p ayl289fo_web < database.sql
```

### 3. Veritabanı Bağlantısını Yapılandırın
`config.php` dosyasındaki veritabanı bilgilerini güncelleyin:

```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'ayl289fo_web');
define('DB_USER', 'ayl289fo_web');
define('DB_PASS', 'QpSfzfHVxQx67hPsq4Ma');
```

### 4. Dosya İzinlerini Ayarlayın
```bash
# Web sunucusunun yazma izni vermesi gereken dizinler
chmod 755 /var/www/html/
chmod 644 /var/www/html/*.php
chmod 644 /var/www/html/*.html
chmod 644 /var/www/html/*.css
chmod 644 /var/www/html/*.js
```

### 5. PHP Ayarlarını Kontrol Edin
`php.ini` dosyasında aşağıdaki ayarları kontrol edin:

```ini
session.cookie_httponly = 1
session.cookie_secure = 1
session.use_strict_mode = 1
session.cookie_samesite = "Strict"
```

## 🔑 Demo Hesaplar

Sistem kurulduktan sonra aşağıdaki demo hesapları kullanabilirsiniz:

### Admin Hesabı
- **Kullanıcı Adı**: `admin`
- **E-posta**: `<EMAIL>`
- **Şifre**: `123456`
- **Rol**: Administrator
- **Admin Panel**: `admin.html` sayfasından erişilebilir

### Demo Hesabı
- **Kullanıcı Adı**: `demo`
- **E-posta**: `<EMAIL>`
- **Şifre**: `123456`
- **Rol**: User

> ⚠️ **Güvenlik Uyarısı**: Demo hesapların şifrelerini mutlaka değiştirin!

## 📁 Dosya Yapısı

```
├── index.html              # Ana login sayfası
├── style.css              # Login sayfası stilleri
├── script.js              # Login sayfası JavaScript'i
├── dashboard.html          # Kullanıcı dashboard sayfası
├── dashboard.css          # Dashboard stilleri
├── dashboard.js           # Dashboard JavaScript'i
├── admin.html             # Admin panel sayfası
├── admin.css              # Admin panel stilleri
├── admin.js               # Admin panel JavaScript'i
├── config.php             # Veritabanı ve güvenlik ayarları
├── login.php              # Login işlemi backend
├── logout.php             # Logout işlemi backend
├── check_session.php      # Session kontrolü
├── check_admin_session.php # Admin session kontrolü
├── admin_api.php          # Admin panel API
├── database.sql           # Veritabanı yapısı
└── README.md              # Bu dosya
```

## 🔧 Yapılandırma

### Güvenlik Ayarları
`config.php` dosyasında aşağıdaki ayarları özelleştirebilirsiniz:

```php
define('SESSION_TIMEOUT', 3600);        // Session süresi (saniye)
define('MAX_LOGIN_ATTEMPTS', 5);        // Maksimum deneme sayısı
define('LOGIN_LOCKOUT_TIME', 900);      // Kilitleme süresi (saniye)
```

### E-posta Ayarları
E-posta bildirimleri için SMTP ayarlarını yapılandırın (gelecek güncellemelerde).

## 🚀 Kullanım

### 1. Login Sayfası
- `index.html` sayfasını ziyaret edin
- Kullanıcı adı/e-posta ve şifrenizi girin
- "Beni Hatırla" seçeneğini işaretleyebilirsiniz
- "Giriş Yap" butonuna tıklayın

### 2. Dashboard - Ürün Arama Sistemi
- **Ürün Arama**: Barkod veya ürün adı ile arama yapabilirsiniz
- **Ürün Detayları**: Ürünlere tıklayarak detaylı bilgileri görüntüleyebilirsiniz
- **Favori Sistem**: Beğendiğiniz ürünleri favorilere ekleyebilirsiniz
- **Son Kullanım Tarihi**: Yakında aktif edilecek özellik
- **Ayarlar**: Profil bilgilerinizi güncelleyebilir, şifre değiştirebilirsiniz

### 3. Admin Panel
- Sadece admin rolündeki kullanıcılar erişebilir
- Kullanıcı yönetimi (ekleme, silme, düzenleme)
- Sistem logları görüntüleme
- Güvenlik raporları
- Sistem ayarları
- Dashboard istatistikleri

### 4. Sorun Giderme
- **Login sorunu yaşıyorsanız**: `login_test.php` sayfasını ziyaret edin
- **Hash sorunu varsa**: `hash_test.php` ile şifre hash'lerini test edin
- **Veritabanı sorunu**: `fix_users.sql` dosyasını çalıştırın

### 3. Güvenlik Özellikleri
- 5 başarısız denemeden sonra hesap 30 dakika kilitlenir
- IP bazlı rate limiting aktiftir
- Session'lar güvenli şekilde yönetilir

## 🔍 Sorun Giderme

### Veritabanı Bağlantı Hatası
```
Çözüm: config.php dosyasındaki veritabanı bilgilerini kontrol edin
```

### Session Hatası
```
Çözüm: PHP session ayarlarını ve dosya izinlerini kontrol edin
```

### 500 Internal Server Error
```
Çözüm: PHP error log'larını kontrol edin ve eksik extension'ları yükleyin
```

## 📊 Veritabanı Tabloları

- **users**: Kullanıcı bilgileri
- **login_attempts**: Giriş denemeleri
- **remember_tokens**: "Beni hatırla" token'ları
- **user_sessions**: Session bilgileri
- **password_reset_tokens**: Şifre sıfırlama token'ları
- **email_verification_tokens**: E-posta doğrulama token'ları
- **system_logs**: Sistem aktivite logları

## 🔄 Güncellemeler

### Gelecek Özellikler
- [ ] E-posta doğrulama sistemi
- [ ] Şifre sıfırlama özelliği
- [ ] İki faktörlü kimlik doğrulama (2FA)
- [ ] Sosyal medya login entegrasyonu
- [ ] Admin paneli
- [ ] Kullanıcı profil yönetimi

## 🤝 Katkıda Bulunma

1. Fork yapın
2. Feature branch oluşturun (`git checkout -b feature/AmazingFeature`)
3. Commit yapın (`git commit -m 'Add some AmazingFeature'`)
4. Branch'i push edin (`git push origin feature/AmazingFeature`)
5. Pull Request oluşturun

## 📄 Lisans

Bu proje MIT lisansı altında lisanslanmıştır. Detaylar için `LICENSE` dosyasına bakın.

## 📞 Destek

Herhangi bir sorun yaşarsanız:
- GitHub Issues kullanın
- Dokümantasyonu kontrol edin
- PHP error log'larını inceleyin

---

**Not**: Bu sistem production ortamında kullanılmadan önce güvenlik testlerinden geçirilmelidir.
