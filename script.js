// Ş<PERSON>re görünürlüğ<PERSON><PERSON>ü değiştir
function togglePassword() {
    const passwordInput = document.getElementById('password');
    const toggleIcon = document.getElementById('toggleIcon');

    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.classList.remove('fa-eye');
        toggleIcon.classList.add('fa-eye-slash');
    } else {
        passwordInput.type = 'password';
        toggleIcon.classList.remove('fa-eye-slash');
        toggleIcon.classList.add('fa-eye');
    }
}

// Şifremi Unuttum Modal'ını göster
function showForgotPassword() {
    document.getElementById('forgotPasswordModal').style.display = 'block';
    resetForgotPasswordForm();
}

// Şifremi Unuttum Modal'ını kapat
function closeForgotPasswordModal() {
    document.getElementById('forgotPasswordModal').style.display = 'none';
    resetForgotPasswordForm();
    stopCountdown();
}

// Form'u sıfırla
function resetForgotPasswordForm() {
    // Tüm adımları gizle
    document.querySelectorAll('.reset-step').forEach(step => {
        step.classList.remove('active');
    });

    // İlk adımı göster
    document.getElementById('step1').classList.add('active');

    // Form'ları temizle
    document.getElementById('usernameForm').reset();
    document.getElementById('otpForm').reset();
    document.getElementById('newPasswordForm').reset();

    // Global değişkenleri sıfırla
    currentUsername = '';
    currentOTPCode = '';
}

// Adım 1'e geri dön
function goBackToStep1() {
    document.querySelectorAll('.reset-step').forEach(step => {
        step.classList.remove('active');
    });
    document.getElementById('step1').classList.add('active');
    stopCountdown();
}

// Global değişkenler
let currentUsername = '';
let currentOTPCode = '';
let countdownInterval = null;

// Kullanıcı adı form submit
document.addEventListener('DOMContentLoaded', function() {
    // Kullanıcı adı formu
    document.getElementById('usernameForm').addEventListener('submit', async function(e) {
        e.preventDefault();

        const username = document.getElementById('resetUsername').value.trim();

        if (!username) {
            showAlert('Kullanıcı adı gerekli!', 'error');
            return;
        }

        try {
            const response = await fetch('otp_api.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'generate_otp',
                    username: username
                })
            });

            const data = await response.json();

            if (data.success) {
                currentUsername = username;
                currentOTPCode = data.otp_code;

                showAlert(`OTP kodu oluşturuldu: ${data.otp_code}`, 'success');

                // Adım 2'ye geç
                document.querySelectorAll('.reset-step').forEach(step => {
                    step.classList.remove('active');
                });
                document.getElementById('step2').classList.add('active');

                // Geri sayımı başlat
                startCountdown(180); // 3 dakika = 180 saniye

            } else {
                showAlert(data.message, 'error');
            }
        } catch (error) {
            console.error('OTP generation error:', error);
            showAlert('OTP oluşturulurken hata oluştu!', 'error');
        }
    });

    // OTP formu
    document.getElementById('otpForm').addEventListener('submit', async function(e) {
        e.preventDefault();

        const otpCode = document.getElementById('otpCode').value.trim();

        if (!otpCode || otpCode.length !== 3) {
            showAlert('3 haneli OTP kodu gerekli!', 'error');
            return;
        }

        try {
            const response = await fetch('otp_api.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'verify_otp',
                    username: currentUsername,
                    otp_code: otpCode
                })
            });

            const data = await response.json();

            if (data.success) {
                showAlert('OTP kodu doğrulandı!', 'success');

                // Adım 3'e geç
                document.querySelectorAll('.reset-step').forEach(step => {
                    step.classList.remove('active');
                });
                document.getElementById('step3').classList.add('active');

                stopCountdown();

            } else {
                showAlert(data.message, 'error');
            }
        } catch (error) {
            console.error('OTP verification error:', error);
            showAlert('OTP doğrulanırken hata oluştu!', 'error');
        }
    });

    // Yeni şifre formu
    document.getElementById('newPasswordForm').addEventListener('submit', async function(e) {
        e.preventDefault();

        const newPassword = document.getElementById('newPassword').value;
        const confirmPassword = document.getElementById('confirmNewPassword').value;

        if (!newPassword || !confirmPassword) {
            showAlert('Tüm alanları doldurun!', 'error');
            return;
        }

        if (newPassword.length < 6) {
            showAlert('Şifre en az 6 karakter olmalı!', 'error');
            return;
        }

        if (newPassword !== confirmPassword) {
            showAlert('Şifreler eşleşmiyor!', 'error');
            return;
        }

        try {
            const response = await fetch('otp_api.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'reset_password',
                    username: currentUsername,
                    otp_code: document.getElementById('otpCode').value,
                    new_password: newPassword
                })
            });

            const data = await response.json();

            if (data.success) {
                showAlert('Şifre başarıyla sıfırlandı! Giriş yapabilirsiniz.', 'success');
                closeForgotPasswordModal();
            } else {
                showAlert(data.message, 'error');
            }
        } catch (error) {
            console.error('Password reset error:', error);
            showAlert('Şifre sıfırlanırken hata oluştu!', 'error');
        }
    });
});

// Geri sayım başlat
function startCountdown(seconds) {
    let timeLeft = seconds;
    const countdownElement = document.getElementById('countdown');

    countdownInterval = setInterval(() => {
        const minutes = Math.floor(timeLeft / 60);
        const secs = timeLeft % 60;

        countdownElement.textContent = `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;

        if (timeLeft <= 0) {
            clearInterval(countdownInterval);
            countdownElement.textContent = '00:00';
            showAlert('OTP kodunun süresi doldu!', 'error');
            goBackToStep1();
        }

        timeLeft--;
    }, 1000);
}

// Geri sayımı durdur
function stopCountdown() {
    if (countdownInterval) {
        clearInterval(countdownInterval);
        countdownInterval = null;
    }
}

// Alert mesajı göster
function showAlert(message, type = 'error') {
    const alertContainer = document.getElementById('alertContainer');
    
    const alert = document.createElement('div');
    alert.className = `alert ${type}`;
    alert.textContent = message;
    
    alertContainer.appendChild(alert);
    
    // 5 saniye sonra alert'i kaldır
    setTimeout(() => {
        alert.remove();
    }, 5000);
}

// Form validasyonu
function validateForm(username, password) {
    if (!username.trim()) {
        showAlert('Kullanıcı adı boş olamaz!', 'error');
        return false;
    }
    
    if (username.length < 3) {
        showAlert('Kullanıcı adı en az 3 karakter olmalıdır!', 'error');
        return false;
    }
    
    if (!password) {
        showAlert('Şifre boş olamaz!', 'error');
        return false;
    }
    
    if (password.length < 6) {
        showAlert('Şifre en az 6 karakter olmalıdır!', 'error');
        return false;
    }
    
    return true;
}

// Loading durumunu değiştir
function setLoading(isLoading) {
    const loginBtn = document.querySelector('.login-btn');
    const btnText = document.querySelector('.btn-text');
    const loadingSpinner = document.querySelector('.loading-spinner');
    
    if (isLoading) {
        loginBtn.disabled = true;
        btnText.style.display = 'none';
        loadingSpinner.style.display = 'block';
        loginBtn.style.opacity = '0.7';
    } else {
        loginBtn.disabled = false;
        btnText.style.display = 'block';
        loadingSpinner.style.display = 'none';
        loginBtn.style.opacity = '1';
    }
}

// Login işlemi
async function handleLogin(username, password, remember) {
    try {
        setLoading(true);
        
        const response = await fetch('login.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                username: username,
                password: password,
                remember: remember
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            showAlert('Giriş başarılı! Yönlendiriliyorsunuz...', 'success');
            
            // Başarılı girişten sonra yönlendirme
            setTimeout(() => {
                window.location.href = data.redirect || 'dashboard.html';
            }, 1500);
        } else {
            showAlert(data.message || 'Giriş başarısız!', 'error');
        }
    } catch (error) {
        console.error('Login error:', error);
        showAlert('Bağlantı hatası! Lütfen tekrar deneyin.', 'error');
    } finally {
        setLoading(false);
    }
}

// Form submit olayı
document.getElementById('loginForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;
    const remember = document.getElementById('remember').checked;
    
    // Form validasyonu
    if (!validateForm(username, password)) {
        return;
    }
    
    // Login işlemini başlat
    handleLogin(username, password, remember);
});

// Input animasyonları
document.querySelectorAll('.input-wrapper input').forEach(input => {
    input.addEventListener('focus', function() {
        this.parentElement.classList.add('focused');
    });
    
    input.addEventListener('blur', function() {
        this.parentElement.classList.remove('focused');
    });
});

// Şifremi unuttum linki
document.querySelector('.forgot-password').addEventListener('click', function(e) {
    e.preventDefault();
    
});

// Enter tuşu ile form submit
document.addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        const loginForm = document.getElementById('loginForm');
        if (document.activeElement.closest('.login-form-container')) {
            loginForm.dispatchEvent(new Event('submit'));
        }
    }
});

// Sayfa yüklendiğinde kullanıcı adı alanına odaklan
window.addEventListener('load', function() {
    document.getElementById('username').focus();
});

// Beni hatırla özelliği için localStorage kontrolü
window.addEventListener('load', function() {
    const rememberedUser = localStorage.getItem('rememberedUser');
    if (rememberedUser) {
        document.getElementById('username').value = rememberedUser;
        document.getElementById('remember').checked = true;
    }
});

// Beni hatırla seçildiğinde kullanıcı adını kaydet
document.getElementById('remember').addEventListener('change', function() {
    const username = document.getElementById('username').value;
    if (this.checked && username) {
        localStorage.setItem('rememberedUser', username);
    } else {
        localStorage.removeItem('rememberedUser');
    }
});

// Kullanıcı adı değiştiğinde beni hatırla durumunu güncelle
document.getElementById('username').addEventListener('input', function() {
    const rememberCheckbox = document.getElementById('remember');
    if (rememberCheckbox.checked) {
        if (this.value) {
            localStorage.setItem('rememberedUser', this.value);
        } else {
            localStorage.removeItem('rememberedUser');
        }
    }
});
