// Dashboard JavaScript

// Global variables
let currentSection = 'search';
let currentProduct = null;

// Sayfa yüklendiğinde çalışacak fonksiyonlar
document.addEventListener('DOMContentLoaded', function() {
    checkAuthentication();
    loadUserInfo();
    initializeDashboard();
    loadFavorites();
    checkAdminAccess();

    // Bugünün tarihini expiry date input'una set et
    const today = new Date().toISOString().split('T')[0];
    const expiryDateInput = document.getElementById('expiryDate');
    if (expiryDateInput) {
        expiryDateInput.min = today;
    }
});

// Kullanıcı kimlik doğrulaması kontrolü
async function checkAuthentication() {
    try {
        const response = await fetch('check_session.php');
        const data = await response.json();
        
        if (!data.authenticated) {
            // Kullanıcı giriş ya<PERSON>, login sayfasına yönlendir
            window.location.href = 'index.html';
            return;
        }
        
        // Kullanıcı bilgilerini güncelle
        updateUserInfo(data.user);
        
    } catch (error) {
        console.error('Authentication check failed:', error);
        showAlert('Kimlik doğrulama hatası!', 'error');
        setTimeout(() => {
            window.location.href = 'index.html';
        }, 2000);
    }
}

// Kullanıcı bilgilerini yükle
function loadUserInfo() {
    // Session storage'dan kullanıcı bilgilerini al
    const userInfo = sessionStorage.getItem('userInfo');
    if (userInfo) {
        const user = JSON.parse(userInfo);
        updateUserInfo(user);
    }
}

// Kullanıcı bilgilerini güncelle
function updateUserInfo(user) {
    const userName = document.getElementById('userName');
    const welcomeUser = document.getElementById('welcomeUser');
    const lastLogin = document.getElementById('lastLogin');
    const adminPanelLink = document.getElementById('adminPanelLink');

    if (userName) userName.textContent = user.username || 'Kullanıcı';
    if (welcomeUser) welcomeUser.textContent = user.username || 'Kullanıcı';

    if (lastLogin && user.last_login) {
        const loginDate = new Date(user.last_login);
        lastLogin.textContent = loginDate.toLocaleString('tr-TR');
    }

    // Admin paneli linkini göster/gizle
    if (adminPanelLink) {
        if (user.role === 'admin') {
            adminPanelLink.style.display = 'flex';
        } else {
            adminPanelLink.style.display = 'none';
        }
    }

    // Admin kontrolü
    if (user.role === 'admin') {
        document.body.classList.add('admin');
    } else {
        document.body.classList.remove('admin');
    }

    // Kullanıcı bilgilerini session storage'a kaydet
    sessionStorage.setItem('userInfo', JSON.stringify(user));
}

// Admin erişim kontrolü
function checkAdminAccess() {
    const userInfo = JSON.parse(sessionStorage.getItem('userInfo') || '{}');

    if (userInfo.role === 'admin') {
        document.body.classList.add('admin');
        console.log('Admin access granted');
    } else {
        document.body.classList.remove('admin');
        console.log('Admin access denied');
    }
}

// Dashboard'u başlat
function initializeDashboard() {
    // Sidebar toggle
    const sidebarToggle = document.querySelector('.sidebar-toggle');
    const sidebar = document.querySelector('.sidebar');

    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('show');
        });
    }

    // Sidebar dışına tıklandığında kapat (mobil)
    document.addEventListener('click', function(e) {
        if (window.innerWidth <= 768) {
            if (!sidebar.contains(e.target) && !sidebarToggle.contains(e.target)) {
                sidebar.classList.remove('show');
            }
        }
    });

    // Bildirim butonu
    const notificationBtn = document.querySelector('.notification-btn');
    if (notificationBtn) {
        notificationBtn.addEventListener('click', function() {
            showAlert('Bildirim özelliği yakında aktif olacak!', 'warning');
        });
    }

    // Navigasyon linkleri
    const navLinks = document.querySelectorAll('.nav-link[data-section]');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const section = this.getAttribute('data-section');
            showSection(section);
        });
    });

    // Dropdown menü linkleri
    const dropdownItems = document.querySelectorAll('.dropdown-item');
    dropdownItems.forEach(item => {
        if (!item.onclick && !item.href.includes('admin.html')) {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                const linkText = this.textContent.trim();
                showAlert(`${linkText} özelliği yakında aktif olacak!`, 'warning');
                toggleUserMenu();
            });
        }
    });

    // Arama formu
    const searchBtn = document.getElementById('searchBtn');
    const productSearch = document.getElementById('productSearch');

    if (searchBtn) {
        searchBtn.addEventListener('click', performSearch);
    }

    if (productSearch) {
        productSearch.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });
    }

    // Modal events
    const modal = document.getElementById('productModal');
    if (modal) {
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                closeProductModal();
            }
        });
    }

    // Expiry form events
    const searchProductBtn = document.getElementById('searchProductBtn');
    const addExpiryBtn = document.getElementById('addExpiryBtn');
    const expiryProductCode = document.getElementById('expiryProductCode');
    const expiryFilter = document.getElementById('expiryFilter');
    const expirySortBy = document.getElementById('expirySortBy');

    if (searchProductBtn) {
        searchProductBtn.addEventListener('click', searchProductForExpiry);
    }

    if (addExpiryBtn) {
        addExpiryBtn.addEventListener('click', addExpiryItem);
    }

    if (expiryProductCode) {
        expiryProductCode.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchProductForExpiry();
            }
        });

        expiryProductCode.addEventListener('input', function() {
            if (this.value.length >= 3) {
                searchProductForExpiry();
            }
        });
    }

    if (expiryFilter) {
        expiryFilter.addEventListener('change', filterExpiryItems);
    }

    if (expirySortBy) {
        expirySortBy.addEventListener('change', sortExpiryItems);
    }
}

// Kullanıcı menüsünü aç/kapat
function toggleUserMenu() {
    const dropdown = document.getElementById('userDropdown');
    dropdown.classList.toggle('show');
}

// Kullanıcı menüsü dışına tıklandığında kapat
document.addEventListener('click', function(e) {
    const userMenu = document.querySelector('.user-menu');
    const dropdown = document.getElementById('userDropdown');
    
    if (!userMenu.contains(e.target)) {
        dropdown.classList.remove('show');
    }
});

// Çıkış yap
async function logout() {
    try {
        showAlert('Çıkış yapılıyor...', 'warning');
        
        const response = await fetch('logout.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });
        
        const data = await response.json();
        
        if (data.success) {
            // Session storage'ı temizle
            sessionStorage.clear();
            localStorage.removeItem('rememberedUser');
            
            showAlert('Başarıyla çıkış yaptınız!', 'success');
            
            // Login sayfasına yönlendir
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 1500);
        } else {
            showAlert('Çıkış yapılırken hata oluştu!', 'error');
        }
    } catch (error) {
        console.error('Logout error:', error);
        showAlert('Bağlantı hatası!', 'error');
        
        // Hata durumunda da çıkış yap
        setTimeout(() => {
            window.location.href = 'index.html';
        }, 2000);
    }
}

// Alert mesajı göster
function showAlert(message, type = 'info') {
    const alertContainer = document.getElementById('alertContainer');
    
    const alert = document.createElement('div');
    alert.className = `alert ${type}`;
    alert.textContent = message;
    
    alertContainer.appendChild(alert);
    
    // 5 saniye sonra alert'i kaldır
    setTimeout(() => {
        alert.remove();
    }, 5000);
}



// Sayfa görünürlüğü değiştiğinde session kontrolü
document.addEventListener('visibilitychange', function() {
    if (!document.hidden) {
        // Sayfa tekrar görünür olduğunda session kontrolü yap
        checkAuthentication();
    }
});

// Klavye kısayolları
document.addEventListener('keydown', function(e) {
    // Ctrl + L: Logout
    if (e.ctrlKey && e.key === 'l') {
        e.preventDefault();
        logout();
    }
    
    // Escape: Menüleri kapat
    if (e.key === 'Escape') {
        const dropdown = document.getElementById('userDropdown');
        dropdown.classList.remove('show');
        
        const sidebar = document.querySelector('.sidebar');
        if (window.innerWidth <= 768) {
            sidebar.classList.remove('show');
        }
    }
});

// Sayfa kapatılırken uyarı (isteğe bağlı)
window.addEventListener('beforeunload', function(e) {
    // Sadece form dolduruluyorsa veya önemli işlem yapılıyorsa uyar
    // e.preventDefault();
    // e.returnValue = '';
});

// Responsive tasarım için window resize olayı
window.addEventListener('resize', function() {
    const sidebar = document.querySelector('.sidebar');
    
    if (window.innerWidth > 768) {
        sidebar.classList.remove('show');
    }
});

// Bölüm göster
function showSection(sectionName) {
    // Tüm bölümleri gizle
    const sections = document.querySelectorAll('.content-section');
    sections.forEach(section => {
        section.classList.remove('active');
    });

    // Seçilen bölümü göster
    const targetSection = document.getElementById(sectionName + '-section');
    if (targetSection) {
        targetSection.classList.add('active');
    }

    // Sidebar navigation güncelle
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach(item => {
        item.classList.remove('active');
    });

    const activeNavItem = document.querySelector(`[data-section="${sectionName}"]`);
    if (activeNavItem) {
        activeNavItem.closest('.nav-item').classList.add('active');
    }

    currentSection = sectionName;

    // Bölüme özel işlemler
    if (sectionName === 'profile') {
        loadProfileData();
    } else if (sectionName === 'expiry') {
        loadExpiryData();
    } else if (sectionName === 'otp') {
        loadOTPData();
    }
}

// Gelişmiş ürün arama
async function performSearch() {
    const searchTerm = document.getElementById('productSearch').value.trim();
    const searchType = document.querySelector('input[name="searchType"]:checked')?.value || 'smart';

    if (!searchTerm) {
        showAlert('Lütfen arama terimi girin!', 'warning');
        return;
    }

    // Minimum 2 karakter kontrolü
    if (searchTerm.length < 2) {
        showAlert('En az 2 karakter girmelisiniz!', 'warning');
        return;
    }

    try {
        // Loading göster
        showLoading('productList');
        showSearchLoading(true);

        const response = await fetch('product_api.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'search',
                term: searchTerm,
                type: searchType,
                limit: 30
            })
        });

        const data = await response.json();

        if (data.success) {
            displaySearchResults(data.products);
            showSearchInfo(data.search_info);

            // Arama geçmişine ekle
            addToSearchHistory(searchTerm, data.count);

            // Başarılı arama mesajı
            if (data.products.length === 0) {
                showAlert(`"${searchTerm}" için sonuç bulunamadı. Farklı kelimeler deneyin.`, 'info');
            } else {
                showAlert(`${data.products.length} ürün bulundu!`, 'success');
            }
        } else {
            showAlert(data.message || 'Arama sırasında hata oluştu!', 'error');
            displaySearchResults([]);
            hideSearchInfo();
        }
    } catch (error) {
        console.error('Search error:', error);
        showAlert('Arama sırasında hata oluştu!', 'error');
        displaySearchResults([]);
        hideSearchInfo();
    } finally {
        hideLoading('productList');
        showSearchLoading(false);
    }
}

// Gelişmiş arama sonuçlarını göster
function displaySearchResults(products) {
    const searchResults = document.getElementById('searchResults');
    const productList = document.getElementById('productList');
    const resultsCount = document.getElementById('resultsCount');

    searchResults.style.display = 'block';
    resultsCount.textContent = `${products.length} ürün bulundu`;

    productList.innerHTML = '';

    if (products.length === 0) {
        productList.innerHTML = `
            <div class="empty-state enhanced">
                <i class="fas fa-search"></i>
                <h3>Ürün bulunamadı</h3>
                <p>Arama teriminizi kontrol edin veya farklı kelimeler deneyin</p>
                <div class="search-tips">
                    <h4><i class="fas fa-lightbulb"></i> Arama İpuçları:</h4>
                    <ul>
                        <li><i class="fas fa-arrow-right"></i> Ürün adının bir kısmını yazın (örn: "şeftali" yerine "şeft")</li>
                        <li><i class="fas fa-arrow-right"></i> Türkçe karakterler kullanmayı deneyin</li>
                        <li><i class="fas fa-arrow-right"></i> Barkod numarasını tam olarak girin</li>
                        <li><i class="fas fa-arrow-right"></i> Birden fazla kelime kullanın</li>
                    </ul>
                </div>
            </div>
        `;
        return;
    }

    products.forEach(product => {
        const productItem = createEnhancedProductItem(product);
        productList.appendChild(productItem);
    });
}

// Ürün öğesi oluştur
function createProductItem(product) {
    const div = document.createElement('div');
    div.className = 'product-item';

    div.innerHTML = `
        <div class="product-info">
            <div class="product-name">${product.name}</div>
            <div class="product-code">
                <span class="code-label">Kod:</span>
                <span class="code-value" onclick="copyToClipboard('${product.code}')" title="Kopyalamak için tıklayın">
                    ${product.code}
                    <i class="fas fa-copy copy-icon"></i>
                </span>
            </div>
        </div>
        <div class="product-actions">
            <button class="action-btn btn-view" onclick="viewProduct('${product.code}')">
                <i class="fas fa-eye"></i>
                Detay
            </button>
            <button class="action-btn btn-favorite" onclick="toggleProductFavorite('${product.code}')">
                <i class="fas fa-heart"></i>
                Favori
            </button>
        </div>
    `;

    return div;
}

// Gelişmiş ürün öğesi oluştur
function createEnhancedProductItem(product) {
    const div = document.createElement('div');
    div.className = 'product-item enhanced';

    const barcodes = product.barcodes && product.barcodes.length > 0
        ? product.barcodes.join(', ')
        : 'Barkod yok';

    const matchType = product.match_type || 'unknown';
    const relevanceScore = product.relevance_score || 0;

    let matchBadge = '';
    let badgeClass = '';

    switch(matchType) {
        case 'exact':
            matchBadge = '<i class="fas fa-bullseye"></i> Tam Eşleşme';
            badgeClass = 'exact';
            break;
        case 'starts_with':
            matchBadge = '<i class="fas fa-play"></i> Başlangıç';
            badgeClass = 'starts';
            break;
        case 'contains':
            matchBadge = '<i class="fas fa-search-plus"></i> İçerik';
            badgeClass = 'contains';
            break;
        case 'fuzzy':
            matchBadge = '<i class="fas fa-magic"></i> Benzer';
            badgeClass = 'fuzzy';
            break;
        case 'word_match':
            matchBadge = '<i class="fas fa-font"></i> Kelime';
            badgeClass = 'word';
            break;
    }

    div.innerHTML = `
        <div class="product-header">
            <div class="product-name">${product.name}</div>
            ${matchBadge ? `<span class="match-badge ${badgeClass}">${matchBadge}</span>` : ''}
        </div>
        <div class="product-details">
            <div class="detail-row">
                <span class="detail-label"><i class="fas fa-barcode"></i> Kod:</span>
                <span class="detail-value clickable" onclick="copyToClipboard('${product.code}')" title="Kopyalamak için tıklayın">
                    ${product.code} <i class="fas fa-copy"></i>
                </span>
            </div>
            <div class="detail-row">
                <span class="detail-label"><i class="fas fa-cube"></i> Birim:</span>
                <span class="detail-value">${product.unit}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label"><i class="fas fa-tag"></i> Fiyat:</span>
                <span class="detail-value">${product.unit_price} ${product.currency}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label"><i class="fas fa-qrcode"></i> Barkodlar:</span>
                <span class="detail-value barcodes clickable" onclick="copyToClipboard('${product.barcodes && product.barcodes[0] ? product.barcodes[0] : ''}')" title="İlk barkodu kopyalamak için tıklayın">
                    ${barcodes} ${product.barcodes && product.barcodes.length > 0 ? '<i class="fas fa-copy"></i>' : ''}
                </span>
            </div>
        </div>
        <div class="product-actions">
            <button class="action-btn btn-copy" onclick="copyToClipboard('${product.code}')" title="Ürün kodunu kopyala">
                <i class="fas fa-copy"></i>
                Kod Kopyala
            </button>
            <button class="action-btn btn-view" onclick="viewProduct('${product.code}')">
                <i class="fas fa-eye"></i>
                Detay
            </button>
            <button class="action-btn btn-favorite" onclick="toggleProductFavorite('${product.code}')">
                <i class="fas fa-heart"></i>
                Favori
            </button>
        </div>
    `;

    // Relevance score'a göre stil ayarla
    if (relevanceScore < 80) {
        div.style.opacity = '0.9';
    }

    return div;
}

// Arama loading göster/gizle
function showSearchLoading(show) {
    const searchBtn = document.getElementById('searchBtn');
    if (searchBtn) {
        if (show) {
            searchBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Arıyor...';
            searchBtn.disabled = true;
        } else {
            searchBtn.innerHTML = '<i class="fas fa-search"></i> Ara';
            searchBtn.disabled = false;
        }
    }
}

// Arama bilgilerini göster
function showSearchInfo(searchInfo) {
    let infoContainer = document.getElementById('searchInfo');

    // Eğer container yoksa oluştur
    if (!infoContainer) {
        infoContainer = document.createElement('div');
        infoContainer.id = 'searchInfo';
        infoContainer.className = 'search-info';

        const searchResults = document.getElementById('searchResults');
        if (searchResults) {
            searchResults.insertBefore(infoContainer, searchResults.firstChild);
        }
    }

    if (!searchInfo) return;

    const totalMatches = searchInfo.total_found || 0;
    let infoHTML = `
        <div class="search-stats">
            <div class="total-results">
                <i class="fas fa-search"></i>
                <strong>${totalMatches}</strong> ürün bulundu
            </div>
    `;

    if (searchInfo.exact_matches > 0) {
        infoHTML += `<div class="exact-matches">
            <i class="fas fa-bullseye"></i> ${searchInfo.exact_matches} tam eşleşme
        </div>`;
    }

    // Arama türlerini göster
    const matchTypes = [];
    if (searchInfo.starts_with_matches > 0) matchTypes.push(`<span class="match-type starts"><i class="fas fa-play"></i> ${searchInfo.starts_with_matches} başlangıç</span>`);
    if (searchInfo.contains_matches > 0) matchTypes.push(`<span class="match-type contains"><i class="fas fa-search-plus"></i> ${searchInfo.contains_matches} içerik</span>`);
    if (searchInfo.fuzzy_matches > 0) matchTypes.push(`<span class="match-type fuzzy"><i class="fas fa-magic"></i> ${searchInfo.fuzzy_matches} benzer</span>`);
    if (searchInfo.word_matches > 0) matchTypes.push(`<span class="match-type word"><i class="fas fa-font"></i> ${searchInfo.word_matches} kelime</span>`);

    if (matchTypes.length > 0) {
        infoHTML += `<div class="match-details">${matchTypes.join(' ')}</div>`;
    }

    infoHTML += '</div>';

    infoContainer.innerHTML = infoHTML;
    infoContainer.style.display = 'block';
}

// Arama bilgilerini gizle
function hideSearchInfo() {
    const infoEl = document.getElementById('searchInfo');
    if (infoEl) {
        infoEl.style.display = 'none';
    }
}

// Arama geçmişine ekle
function addToSearchHistory(term, resultCount) {
    let history = JSON.parse(localStorage.getItem('searchHistory') || '[]');

    // Aynı terimi tekrar ekleme
    history = history.filter(item => item.term !== term);

    // Yeni terimi başa ekle
    history.unshift({
        term: term,
        count: resultCount,
        timestamp: new Date().toISOString()
    });

    // Maksimum 10 arama sakla
    history = history.slice(0, 10);

    localStorage.setItem('searchHistory', JSON.stringify(history));
}

// Ürün detayını göster
async function viewProduct(productCode) {
    try {
        const response = await fetch('product_api.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'get_product',
                code: productCode
            })
        });

        const data = await response.json();

        if (data.success) {
            showProductModal(data.product);
        } else {
            showAlert(data.message || 'Ürün detayları alınamadı!', 'error');
        }
    } catch (error) {
        console.error('Product detail error:', error);
        showAlert('Ürün detayları alınamadı!', 'error');
    }
}

// Ürün modal göster
function showProductModal(product) {
    currentProduct = product;
    const modal = document.getElementById('productModal');
    const productDetails = document.getElementById('productDetails');
    const favoriteBtn = document.getElementById('favoriteBtn');

    // Barkodları listele
    let barcodesHtml = '';
    if (product.barcodes && product.barcodes.length > 0) {
        barcodesHtml = product.barcodes.map(barcode => `
            <div class="barcode-item" onclick="copyToClipboard('${barcode}')" title="Kopyalamak için tıklayın">
                <span class="barcode-value">${barcode}</span>
                <i class="fas fa-copy copy-icon"></i>
            </div>
        `).join('');
    } else {
        barcodesHtml = '<div class="no-barcode">Barkod bulunamadı</div>';
    }

    productDetails.innerHTML = `
        <div class="product-detail">
            <div class="detail-item">
                <span class="detail-label">Ürün Adı:</span>
                <span class="detail-value copyable" onclick="copyToClipboard('${product.name}')" title="Kopyalamak için tıklayın">
                    ${product.name}
                    <i class="fas fa-copy copy-icon"></i>
                </span>
            </div>
            <div class="detail-item">
                <span class="detail-label">Ürün Kodu:</span>
                <span class="detail-value copyable" onclick="copyToClipboard('${product.code}')" title="Kopyalamak için tıklayın">
                    ${product.code}
                    <i class="fas fa-copy copy-icon"></i>
                </span>
            </div>
            <div class="detail-item barcodes-section">
                <span class="detail-label">Barkodlar:</span>
                <div class="barcodes-list">
                    ${barcodesHtml}
                </div>
            </div>
        </div>
    `;

    // Favori durumunu kontrol et
    checkFavoriteStatus(product.code).then(isFavorite => {
        if (isFavorite) {
            favoriteBtn.innerHTML = '<i class="fas fa-heart"></i> Favorilerden Çıkar';
            favoriteBtn.classList.add('active');
        } else {
            favoriteBtn.innerHTML = '<i class="fas fa-heart"></i> Favorilere Ekle';
            favoriteBtn.classList.remove('active');
        }
    });

    modal.classList.add('show');
}

// Ürün modal kapat
function closeProductModal() {
    const modal = document.getElementById('productModal');
    modal.classList.remove('show');
    currentProduct = null;
}

// Favori durumunu değiştir
async function toggleFavorite() {
    if (!currentProduct) return;

    try {
        const response = await fetch('product_api.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'toggle_favorite',
                product_code: currentProduct.code
            })
        });

        const data = await response.json();

        if (data.success) {
            const favoriteBtn = document.getElementById('favoriteBtn');
            if (data.is_favorite) {
                favoriteBtn.innerHTML = '<i class="fas fa-heart"></i> Favorilerden Çıkar';
                favoriteBtn.classList.add('active');
                showAlert('Ürün favorilere eklendi!', 'success');
            } else {
                favoriteBtn.innerHTML = '<i class="fas fa-heart"></i> Favorilere Ekle';
                favoriteBtn.classList.remove('active');
                showAlert('Ürün favorilerden çıkarıldı!', 'success');
            }

            // Favori listesini yenile
            loadFavorites();
        } else {
            showAlert(data.message || 'Favori işlemi başarısız!', 'error');
        }
    } catch (error) {
        console.error('Favorite toggle error:', error);
        showAlert('Favori işlemi başarısız!', 'error');
    }
}

// Ürün favori durumunu değiştir (liste için)
async function toggleProductFavorite(productCode) {
    try {
        const response = await fetch('product_api.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'toggle_favorite',
                product_code: productCode
            })
        });

        const data = await response.json();

        if (data.success) {
            if (data.is_favorite) {
                showAlert('Ürün favorilere eklendi!', 'success');
            } else {
                showAlert('Ürün favorilerden çıkarıldı!', 'success');
            }

            loadFavorites();
        } else {
            showAlert(data.message || 'Favori işlemi başarısız!', 'error');
        }
    } catch (error) {
        console.error('Favorite toggle error:', error);
        showAlert('Favori işlemi başarısız!', 'error');
    }
}

// Favori durumunu kontrol et
async function checkFavoriteStatus(productCode) {
    try {
        const response = await fetch('product_api.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'check_favorite',
                product_code: productCode
            })
        });

        const data = await response.json();
        return data.success && data.is_favorite;
    } catch (error) {
        console.error('Check favorite error:', error);
        return false;
    }
}

// Favorileri yükle
async function loadFavorites() {
    try {
        const response = await fetch('product_api.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'get_favorites'
            })
        });

        const data = await response.json();

        if (data.success) {
            displayFavorites(data.favorites);
        }
    } catch (error) {
        console.error('Load favorites error:', error);
    }
}

// Favorileri göster
function displayFavorites(favorites) {
    const favoritesList = document.getElementById('favoritesList');

    if (!favoritesList) return;

    favoritesList.innerHTML = '';

    if (favorites.length === 0) {
        favoritesList.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-heart"></i>
                <h3>Henüz favori ürün yok</h3>
                <p>Beğendiğiniz ürünleri favorilere ekleyebilirsiniz</p>
            </div>
        `;
        return;
    }

    favorites.forEach(favorite => {
        const favoriteItem = createFavoriteItem(favorite);
        favoritesList.appendChild(favoriteItem);
    });
}

// Favori öğesi oluştur
function createFavoriteItem(favorite) {
    const div = document.createElement('div');
    div.className = 'favorite-item';

    div.innerHTML = `
        <div class="product-name">${favorite.name}</div>
        <div class="product-code">
            <span class="code-label">Kod:</span>
            <span class="code-value" onclick="copyToClipboard('${favorite.code}')" title="Kopyalamak için tıklayın">
                ${favorite.code}
                <i class="fas fa-copy copy-icon"></i>
            </span>
        </div>
        <div class="product-actions" style="margin-top: 15px;">
            <button class="action-btn btn-view" onclick="viewProduct('${favorite.code}')">
                <i class="fas fa-eye"></i>
                Detay
            </button>
            <button class="action-btn btn-favorite active" onclick="toggleProductFavorite('${favorite.code}')">
                <i class="fas fa-heart"></i>
                Çıkar
            </button>
        </div>
    `;

    return div;
}



// Şifre değiştir
async function changePassword() {
    const currentPassword = document.getElementById('currentPassword').value;
    const newPassword = document.getElementById('newPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;

    if (!currentPassword || !newPassword || !confirmPassword) {
        showAlert('Tüm şifre alanlarını doldurun!', 'warning');
        return;
    }

    if (newPassword !== confirmPassword) {
        showAlert('Yeni şifreler eşleşmiyor!', 'error');
        return;
    }

    if (newPassword.length < 6) {
        showAlert('Yeni şifre en az 6 karakter olmalıdır!', 'error');
        return;
    }

    try {
        const response = await fetch('user_api.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'change_password',
                current_password: currentPassword,
                new_password: newPassword
            })
        });

        const data = await response.json();

        if (data.success) {
            showAlert('Şifre başarıyla değiştirildi!', 'success');
            document.getElementById('currentPassword').value = '';
            document.getElementById('newPassword').value = '';
            document.getElementById('confirmPassword').value = '';
        } else {
            showAlert(data.message || 'Şifre değiştirilemedi!', 'error');
        }
    } catch (error) {
        console.error('Change password error:', error);
        showAlert('Şifre değiştirilemedi!', 'error');
    }
}

// Panoya kopyala
function copyToClipboard(text) {
    if (navigator.clipboard && window.isSecureContext) {
        // Modern API kullan
        navigator.clipboard.writeText(text).then(() => {
            showAlert('Kopyalandı: ' + text, 'success');
        }).catch(err => {
            console.error('Kopyalama hatası:', err);
            fallbackCopyTextToClipboard(text);
        });
    } else {
        // Fallback method
        fallbackCopyTextToClipboard(text);
    }
}

// Fallback kopyalama metodu
function fallbackCopyTextToClipboard(text) {
    const textArea = document.createElement("textarea");
    textArea.value = text;

    // Görünmez yap
    textArea.style.top = "0";
    textArea.style.left = "0";
    textArea.style.position = "fixed";
    textArea.style.opacity = "0";

    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        const successful = document.execCommand('copy');
        if (successful) {
            showAlert('Kopyalandı: ' + text, 'success');
        } else {
            showAlert('Kopyalama başarısız!', 'error');
        }
    } catch (err) {
        console.error('Fallback kopyalama hatası:', err);
        showAlert('Kopyalama başarısız!', 'error');
    }

    document.body.removeChild(textArea);
}

// Loading göster
function showLoading(elementId) {
    const element = document.getElementById(elementId);
    if (element) {
        element.innerHTML = `
            <div class="loading">
                <div class="loading-spinner"></div>
            </div>
        `;
    }
}

// Profil fonksiyonları
function loadProfileData() {
    loadUserProfile();
    loadProfileStats();
    loadUserActivity();
    loadNotificationSettings();
}

// Kullanıcı profil bilgilerini yükle
async function loadUserProfile() {
    try {
        const response = await fetch('user_api.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'get_profile'
            })
        });

        const data = await response.json();

        if (data.success) {
            const user = data.user;

            // Header ve profil bilgilerini güncelle
            const fullName = `${user.first_name || ''} ${user.last_name || ''}`.trim();
            const displayName = fullName || user.username;

            document.getElementById('profileName').textContent = displayName;
            document.getElementById('profileEmail').textContent = user.email;

            // Header'daki kullanıcı adını da güncelle
            const headerUserName = document.getElementById('userName');
            if (headerUserName) {
                headerUserName.textContent = displayName;
            }

            // Form alanlarını doldur
            document.getElementById('profileFirstName').value = user.first_name || '';
            document.getElementById('profileLastName').value = user.last_name || '';
            document.getElementById('profileEmailInput').value = user.email || '';
            document.getElementById('profilePhone').value = user.phone || '';
            document.getElementById('profileBio').value = user.bio || '';

            // Üyelik süresini hesapla
            if (user.created_at) {
                const createdDate = new Date(user.created_at);
                const now = new Date();
                const diffTime = Math.abs(now - createdDate);
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                document.getElementById('memberDays').textContent = diffDays;
            }
        }
    } catch (error) {
        console.error('Load profile error:', error);
    }
}

// Profil istatistiklerini yükle
async function loadProfileStats() {
    try {
        const response = await fetch('user_api.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'get_profile_stats'
            })
        });

        const data = await response.json();

        if (data.success) {
            document.getElementById('favoriteCount').textContent = data.stats.favorite_count || 0;
            document.getElementById('searchCount').textContent = data.stats.search_count || 0;
        }
    } catch (error) {
        console.error('Load profile stats error:', error);
    }
}

// Kullanıcı aktivitelerini yükle
async function loadUserActivity() {
    try {
        const response = await fetch('user_api.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'get_user_activity'
            })
        });

        const data = await response.json();

        if (data.success) {
            displayUserActivity(data.activities);
        }
    } catch (error) {
        console.error('Load user activity error:', error);
    }
}

// Kullanıcı aktivitelerini göster
function displayUserActivity(activities) {
    const activityList = document.getElementById('activityList');

    if (!activityList) return;

    activityList.innerHTML = '';

    if (activities.length === 0) {
        activityList.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-history"></i>
                <h3>Henüz aktivite yok</h3>
                <p>Sistem kullanımınız burada görünecek</p>
            </div>
        `;
        return;
    }

    activities.forEach(activity => {
        const activityItem = document.createElement('div');
        activityItem.className = 'activity-item';

        const icon = getActivityIcon(activity.action);
        const date = new Date(activity.created_at).toLocaleString('tr-TR');

        activityItem.innerHTML = `
            <div class="activity-icon">
                <i class="fas ${icon}"></i>
            </div>
            <div class="activity-content">
                <h4>${activity.description}</h4>
                <p>IP: ${activity.ip_address}</p>
                <span class="activity-time">${date}</span>
            </div>
        `;

        activityList.appendChild(activityItem);
    });
}

// Aktivite ikonunu al
function getActivityIcon(action) {
    const icons = {
        'login': 'fa-sign-in-alt',
        'logout': 'fa-sign-out-alt',
        'profile_updated': 'fa-user-edit',
        'password_changed': 'fa-key',
        'favorite_added': 'fa-heart',
        'favorite_removed': 'fa-heart-broken',
        'search': 'fa-search'
    };

    return icons[action] || 'fa-info-circle';
}

// Profil tab'larını göster
function showProfileTab(tabName) {
    // Tüm tab'ları gizle
    const tabs = document.querySelectorAll('.tab-content');
    tabs.forEach(tab => {
        tab.classList.remove('active');
    });

    // Tüm tab butonlarını pasif yap
    const tabBtns = document.querySelectorAll('.tab-btn');
    tabBtns.forEach(btn => {
        btn.classList.remove('active');
    });

    // Seçilen tab'ı göster
    const targetTab = document.getElementById(tabName + '-tab');
    if (targetTab) {
        targetTab.classList.add('active');
    }

    // Seçilen tab butonunu aktif yap
    const activeBtn = document.querySelector(`[onclick="showProfileTab('${tabName}')"]`);
    if (activeBtn) {
        activeBtn.classList.add('active');
    }

    // Sekmeye özel işlemler
    if (tabName === 'notifications') {
        loadNotificationSettings();
    }
}

// Profil bilgilerini kaydet
async function saveProfileInfo() {
    const profileData = {
        first_name: document.getElementById('profileFirstName').value,
        last_name: document.getElementById('profileLastName').value,
        phone: document.getElementById('profilePhone').value,
        bio: document.getElementById('profileBio').value
    };

    try {
        const response = await fetch('user_api.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'update_profile',
                ...profileData
            })
        });

        const data = await response.json();

        if (data.success) {
            showAlert('Profil bilgileri başarıyla güncellendi!', 'success');
            loadProfileData(); // Profili yeniden yükle
        } else {
            showAlert(data.message || 'Profil güncellenemedi!', 'error');
        }
    } catch (error) {
        console.error('Save profile error:', error);
        showAlert('Profil güncellenemedi!', 'error');
    }
}

// Profil formunu sıfırla
function resetProfileForm() {
    loadUserProfile();
    showAlert('Form sıfırlandı!', 'info');
}

// Şifre değiştirme formunu göster
function showPasswordChangeForm() {
    const form = document.getElementById('passwordChangeForm');
    form.style.display = 'block';
}

// Şifre değiştirme formunu gizle
function hidePasswordChangeForm() {
    const form = document.getElementById('passwordChangeForm');
    form.style.display = 'none';

    // Formu temizle
    document.getElementById('currentPasswordProfile').value = '';
    document.getElementById('newPasswordProfile').value = '';
    document.getElementById('confirmPasswordProfile').value = '';
}

// Profil şifre değiştir
async function changePasswordProfile() {
    const currentPassword = document.getElementById('currentPasswordProfile').value;
    const newPassword = document.getElementById('newPasswordProfile').value;
    const confirmPassword = document.getElementById('confirmPasswordProfile').value;

    if (!currentPassword || !newPassword || !confirmPassword) {
        showAlert('Tüm şifre alanlarını doldurun!', 'warning');
        return;
    }

    if (newPassword !== confirmPassword) {
        showAlert('Yeni şifreler eşleşmiyor!', 'error');
        return;
    }

    if (newPassword.length < 6) {
        showAlert('Yeni şifre en az 6 karakter olmalıdır!', 'error');
        return;
    }

    try {
        const response = await fetch('user_api.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'change_password',
                current_password: currentPassword,
                new_password: newPassword
            })
        });

        const data = await response.json();

        if (data.success) {
            showAlert('Şifre başarıyla değiştirildi!', 'success');
            hidePasswordChangeForm();
        } else {
            showAlert(data.message || 'Şifre değiştirilemedi!', 'error');
        }
    } catch (error) {
        console.error('Change password error:', error);
        showAlert('Şifre değiştirilemedi!', 'error');
    }
}

// Profil resmi değiştir
function changeProfilePicture() {
    showAlert('Profil resmi değiştirme özelliği yakında aktif edilecek!', 'info');
}

// Giriş geçmişini göster
function showLoginHistory() {
    showAlert('Giriş geçmişi özelliği yakında aktif edilecek!', 'info');
}

// Bildirim ayarlarını yükle
async function loadNotificationSettings() {
    try {
        const response = await fetch('user_api.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'get_notification_settings'
            })
        });

        const data = await response.json();

        if (data.success) {
            const settings = data.settings;

            // Checkbox'ları güncelle
            document.getElementById('emailNotifications').checked = settings.email_notifications || false;
            document.getElementById('expiryEmailNotifications').checked = settings.expiry_email_notifications || false;
            document.getElementById('pushNotifications').checked = settings.push_notifications || false;
            document.getElementById('criticalPushNotifications').checked = settings.critical_push_notifications || false;
            document.getElementById('smsNotifications').checked = settings.sms_notifications || false;

            // Timing ayarları
            document.getElementById('criticalDays').value = settings.critical_days || 3;
            document.getElementById('warningDays').value = settings.warning_days || 7;
        }
    } catch (error) {
        console.error('Load notification settings error:', error);
    }
}

// Bildirim ayarlarını kaydet
async function saveNotificationSettings() {
    const settings = {
        email_notifications: document.getElementById('emailNotifications').checked,
        expiry_email_notifications: document.getElementById('expiryEmailNotifications').checked,
        push_notifications: document.getElementById('pushNotifications').checked,
        critical_push_notifications: document.getElementById('criticalPushNotifications').checked,
        sms_notifications: document.getElementById('smsNotifications').checked,
        critical_days: parseInt(document.getElementById('criticalDays').value),
        warning_days: parseInt(document.getElementById('warningDays').value)
    };

    try {
        const response = await fetch('user_api.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'save_notification_settings',
                ...settings
            })
        });

        const data = await response.json();

        if (data.success) {
            showAlert('Bildirim ayarları başarıyla kaydedildi!', 'success');
        } else {
            showAlert(data.message || 'Ayarlar kaydedilemedi!', 'error');
        }
    } catch (error) {
        console.error('Save notification settings error:', error);
        showAlert('Ayarlar kaydedilemedi!', 'error');
    }
}

// Bildirim ayarlarını sıfırla
function resetNotificationSettings() {
    // Varsayılan değerlere dön
    document.getElementById('emailNotifications').checked = true;
    document.getElementById('expiryEmailNotifications').checked = true;
    document.getElementById('pushNotifications').checked = true;
    document.getElementById('criticalPushNotifications').checked = true;
    document.getElementById('smsNotifications').checked = false;
    document.getElementById('criticalDays').value = 3;
    document.getElementById('warningDays').value = 7;

    showAlert('Bildirim ayarları varsayılan değerlere sıfırlandı!', 'info');
}

// Son kullanım tarihi fonksiyonları
let expiryItems = [];

// Son kullanım tarihi verilerini yükle
function loadExpiryData() {
    console.log('Loading expiry data...');
    loadExpiryItems();
    loadExpiryStats();
}

// Ürün arama (son kullanım için)
async function searchProductForExpiry() {
    const productCode = document.getElementById('expiryProductCode').value.trim();
    const productNameField = document.getElementById('expiryProductName');

    if (!productCode) {
        productNameField.value = '';
        return;
    }

    console.log('Searching product:', productCode);

    try {
        const response = await fetch('product_api.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'get_product',
                code: productCode
            })
        });

        console.log('Product search response status:', response.status);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('Product search data:', data);

        if (data.success) {
            productNameField.value = data.product.name;
            productNameField.style.color = '#10b981';
        } else {
            productNameField.value = 'Ürün bulunamadı';
            productNameField.style.color = '#ef4444';
        }
    } catch (error) {
        console.error('Product search error:', error);
        productNameField.value = 'Arama hatası: ' + error.message;
        productNameField.style.color = '#ef4444';
    }
}

// Resim önizleme
function previewExpiryImage() {
    const imageUrl = document.getElementById('expiryImageUrl').value.trim();
    const previewContainer = document.getElementById('expiryImagePreview');
    const previewImg = document.getElementById('expiryPreviewImg');

    if (!imageUrl) {
        previewContainer.style.display = 'none';
        return;
    }

    // URL formatını kontrol et
    if (!isValidImageUrl(imageUrl)) {
        showAlert('Geçerli bir resim URL\'si girin!', 'error');
        return;
    }

    // Resmi yükle
    previewImg.onload = function() {
        previewContainer.style.display = 'block';
        showAlert('Resim başarıyla yüklendi!', 'success');
    };

    previewImg.onerror = function() {
        previewContainer.style.display = 'none';
        showAlert('Resim yüklenemedi! URL\'yi kontrol edin.', 'error');
    };

    previewImg.src = imageUrl;
}

// Resmi kaldır
function removeExpiryImage() {
    document.getElementById('expiryImageUrl').value = '';
    document.getElementById('expiryImagePreview').style.display = 'none';
    showAlert('Resim kaldırıldı!', 'info');
}

// URL doğrulama
function isValidImageUrl(url) {
    try {
        const urlObj = new URL(url);
        const validExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp'];
        const pathname = urlObj.pathname.toLowerCase();
        return validExtensions.some(ext => pathname.endsWith(ext)) ||
               url.includes('imgur.com') ||
               url.includes('cloudinary.com') ||
               url.includes('unsplash.com') ||
               url.includes('pexels.com');
    } catch {
        return false;
    }
}

// Form temizle
function clearExpiryForm() {
    document.getElementById('expiryProductCode').value = '';
    document.getElementById('expiryProductName').value = '';
    document.getElementById('expiryDate').value = '';
    document.getElementById('expiryQuantity').value = '1';
    document.getElementById('expiryImageUrl').value = '';
    document.getElementById('expiryNotes').value = '';
    document.getElementById('expiryImagePreview').style.display = 'none';

    showAlert('Form temizlendi!', 'info');
}

// Son kullanım tarihi öğesi ekle
async function addExpiryItem() {
    const productCode = document.getElementById('expiryProductCode').value.trim();
    const productName = document.getElementById('expiryProductName').value.trim();
    const expiryDate = document.getElementById('expiryDate').value;
    const quantity = document.getElementById('expiryQuantity').value || 1;
    const imageUrl = document.getElementById('expiryImageUrl').value.trim();
    const notes = document.getElementById('expiryNotes').value.trim();

    if (!productCode || !productName || !expiryDate) {
        showAlert('Ürün kodu, adı ve son kullanım tarihi gereklidir!', 'error');
        return;
    }

    if (productName === 'Ürün bulunamadı' || productName === 'Arama hatası') {
        showAlert('Geçerli bir ürün kodu girin!', 'error');
        return;
    }

    // Resim URL'si varsa doğrula
    if (imageUrl && !isValidImageUrl(imageUrl)) {
        showAlert('Geçerli bir resim URL\'si girin!', 'error');
        return;
    }

    try {
        const response = await fetch('expiry_api.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'add_expiry',
                product_code: productCode,
                product_name: productName,
                expiry_date: expiryDate,
                quantity: parseInt(quantity),
                image_url: imageUrl,
                notes: notes
            })
        });

        const data = await response.json();

        if (data.success) {
            showAlert('Ürün başarıyla eklendi!', 'success');

            // Formu temizle
            clearExpiryForm();

            // Listeyi yenile
            loadExpiryData();
        } else {
            showAlert(data.message || 'Ürün eklenemedi!', 'error');
        }
    } catch (error) {
        console.error('Add expiry error:', error);
        showAlert('Ürün eklenemedi!', 'error');
    }
}

// Son kullanım tarihi öğelerini yükle
async function loadExpiryItems() {
    console.log('Loading expiry items...');
    try {
        const response = await fetch('expiry_api.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'get_expiry_items'
            })
        });

        console.log('Response status:', response.status);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('Expiry items data:', data);

        if (data.success) {
            expiryItems = data.items;
            displayExpiryItems(expiryItems);
            loadExpiryStats(); // İstatistikleri de güncelle
        } else {
            console.error('API Error:', data.message);
            showAlert(data.message || 'Veriler yüklenemedi!', 'error');
        }
    } catch (error) {
        console.error('Load expiry items error:', error);
        showAlert('Veriler yüklenirken hata oluştu: ' + error.message, 'error');

        // Boş liste göster
        displayExpiryItems([]);
    }
}

// Son kullanım tarihi öğelerini göster
function displayExpiryItems(items) {
    const expiryList = document.getElementById('expiryList');
    const expiryCount = document.getElementById('expiryCount');

    if (!expiryList) return;

    expiryCount.textContent = `${items.length} ürün`;
    expiryList.innerHTML = '';

    if (items.length === 0) {
        expiryList.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-calendar-times"></i>
                <h3>Henüz ürün eklenmemiş</h3>
                <p>Takip etmek istediğiniz ürünleri ekleyin</p>
            </div>
        `;
        return;
    }

    // Tarihe göre sırala (yakın tarihler üstte)
    items.sort((a, b) => new Date(a.expiry_date) - new Date(b.expiry_date));

    items.forEach(item => {
        const expiryItem = createExpiryItem(item);
        expiryList.appendChild(expiryItem);
    });
}

// Son kullanım tarihi öğesi oluştur
function createExpiryItem(item) {
    const div = document.createElement('div');

    const expiryDate = new Date(item.expiry_date);
    const today = new Date();
    const diffTime = expiryDate - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    let statusClass = 'normal';
    let statusText = `${diffDays} gün kaldı`;
    let alertMessage = '';

    if (diffDays < 0) {
        statusClass = 'expired';
        statusText = `${Math.abs(diffDays)} gün geçmiş`;
        alertMessage = '⚠️ İmha edilmeli!';
    } else if (diffDays <= 3) {
        statusClass = 'critical';
        alertMessage = '🚨 Başka mağazaya transfer edin veya personele hedef verin!';
    } else if (diffDays <= 7) {
        statusClass = 'warning';
        alertMessage = '⚠️ Dikkat! Tarih yaklaşıyor.';
    }

    div.className = `expiry-item ${statusClass}`;

    div.innerHTML = `
        ${item.product_image_url ? `
            <div class="expiry-image">
                <img src="${item.product_image_url}" alt="${item.product_name}" onerror="this.style.display='none'">
            </div>
        ` : ''}
        <div class="expiry-info ${item.product_image_url ? 'with-image' : ''}">
            <div class="expiry-product-name">${item.product_name}</div>
            <div class="expiry-product-code">Kod: ${item.product_code}</div>
            <div class="expiry-date-info">
                <span class="expiry-date">${expiryDate.toLocaleDateString('tr-TR')}</span>
                <span class="expiry-days">${statusText}</span>
                ${alertMessage ? `<span class="expiry-alert-msg" style="color: #ef4444; font-weight: 600;">${alertMessage}</span>` : ''}
            </div>
            ${item.notes ? `<div class="expiry-notes"><i class="fas fa-sticky-note"></i> ${item.notes}</div>` : ''}
        </div>
        <div class="expiry-quantity">
            <i class="fas fa-boxes"></i>
            ${item.quantity} adet
        </div>
        <div class="expiry-actions">
            <button class="expiry-action-btn btn-share" onclick="shareExpiryItem('${item.id}')">
                <i class="fas fa-share"></i>
                Paylaş
            </button>
            <button class="expiry-action-btn btn-edit" onclick="editExpiryItem('${item.id}')">
                <i class="fas fa-edit"></i>
                Düzenle
            </button>
            <button class="expiry-action-btn btn-delete" onclick="deleteExpiryItem('${item.id}')">
                <i class="fas fa-trash"></i>
                Sil
            </button>
        </div>
    `;

    return div;
}

// Son kullanım tarihi istatistiklerini yükle
function loadExpiryStats() {
    if (!expiryItems.length) {
        document.getElementById('criticalCount').textContent = '0';
        document.getElementById('warningCount').textContent = '0';
        document.getElementById('expiredCount').textContent = '0';
        document.getElementById('totalCount').textContent = '0';
        return;
    }

    const today = new Date();
    let critical = 0, warning = 0, expired = 0;

    expiryItems.forEach(item => {
        const expiryDate = new Date(item.expiry_date);
        const diffDays = Math.ceil((expiryDate - today) / (1000 * 60 * 60 * 24));

        if (diffDays < 0) {
            expired++;
        } else if (diffDays <= 3) {
            critical++;
        } else if (diffDays <= 7) {
            warning++;
        }
    });

    document.getElementById('criticalCount').textContent = critical;
    document.getElementById('warningCount').textContent = warning;
    document.getElementById('expiredCount').textContent = expired;
    document.getElementById('totalCount').textContent = expiryItems.length;
}

// Ürün paylaş
function shareExpiryItem(itemId) {
    const item = expiryItems.find(i => i.id == itemId);
    if (!item) return;

    const expiryDate = new Date(item.expiry_date);
    const today = new Date();
    const diffDays = Math.ceil((expiryDate - today) / (1000 * 60 * 60 * 24));

    let statusText = '';
    if (diffDays < 0) {
        statusText = `⚠️ SÜRESİ GEÇMİŞ (${Math.abs(diffDays)} gün)`;
    } else if (diffDays <= 3) {
        statusText = `🚨 KRİTİK (${diffDays} gün kaldı)`;
    } else if (diffDays <= 7) {
        statusText = `⚠️ UYARI (${diffDays} gün kaldı)`;
    } else {
        statusText = `✅ NORMAL (${diffDays} gün kaldı)`;
    }

    const shareContent = `
        📦 ÜRÜN BİLGİSİ

        🏷️ Ürün: ${item.product_name}
        🔢 Kod: ${item.product_code}
        📅 Son Kullanım: ${expiryDate.toLocaleDateString('tr-TR')}
        📊 Durum: ${statusText}
        📦 Adet: ${item.quantity}

        ${diffDays <= 3 && diffDays >= 0 ? '🚨 ACİL: Başka mağazaya transfer edin veya personele hedef verin!' : ''}
        ${diffDays < 0 ? '⚠️ DİKKAT: Ürün imha edilmelidir!' : ''}

        📱 Sistem: Son Kullanım Tarihi Takibi
    `;

    const modal = document.getElementById('shareModal');
    const shareContentDiv = document.getElementById('shareContent');

    shareContentDiv.innerHTML = `
        <div style="background: #f9fafb; padding: 20px; border-radius: 8px; font-family: monospace; white-space: pre-line; font-size: 14px; line-height: 1.6;">
            ${shareContent}
        </div>
    `;

    modal.classList.add('show');
}

// Paylaşım modal kapat
function closeShareModal() {
    const modal = document.getElementById('shareModal');
    modal.classList.remove('show');
}

// Paylaşım içeriğini kopyala
function copyShareContent() {
    const shareContent = document.getElementById('shareContent').textContent;
    copyToClipboard(shareContent);
    closeShareModal();
}

// Ürün düzenle
function editExpiryItem(itemId) {
    showAlert('Düzenleme özelliği yakında aktif edilecek!', 'info');
}

// ==========================================
// OTP YÖNETİMİ FONKSİYONLARI
// ==========================================

let otpList = [];

// OTP verilerini yükle
function loadOTPData() {
    console.log('Loading OTP data...');
    loadOTPList();
}

// OTP listesini yükle
async function loadOTPList() {
    try {
        const response = await fetch('otp_api.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'get_otp_list',
                limit: 100
            })
        });

        const data = await response.json();

        if (data.success) {
            otpList = data.otps;
            displayOTPList(otpList);
            updateOTPStats(otpList);
        } else {
            showAlert(data.message || 'OTP listesi yüklenemedi!', 'error');
        }
    } catch (error) {
        console.error('Load OTP list error:', error);
        showAlert('OTP listesi yüklenirken hata oluştu!', 'error');
    }
}

// OTP listesini göster
function displayOTPList(otps) {
    const otpListContainer = document.getElementById('otpList');

    if (!otpListContainer) return;

    if (otps.length === 0) {
        otpListContainer.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-key"></i>
                <h3>OTP bulunamadı</h3>
                <p>Henüz şifre sıfırlama talebi yok</p>
            </div>
        `;
        return;
    }

    const otpsHTML = otps.map(otp => {
        const createdDate = new Date(otp.created_at);
        const expiresDate = new Date(otp.expires_at);
        const now = new Date();

        let statusClass = '';
        let statusIcon = '';
        let statusText = '';

        switch(otp.status) {
            case 'active':
                statusClass = 'active';
                statusIcon = 'fa-clock';
                statusText = 'Aktif';
                break;
            case 'used':
                statusClass = 'used';
                statusIcon = 'fa-check';
                statusText = 'Kullanılmış';
                break;
            case 'expired':
                statusClass = 'expired';
                statusIcon = 'fa-times';
                statusText = 'Süresi Dolmuş';
                break;
        }

        const timeLeft = otp.status === 'active' ? Math.max(0, Math.floor((expiresDate - now) / 1000)) : 0;
        const timeLeftText = timeLeft > 0 ? `${Math.floor(timeLeft / 60)}:${(timeLeft % 60).toString().padStart(2, '0')}` : '00:00';

        return `
            <div class="otp-item ${statusClass}" data-status="${otp.status}">
                <div class="otp-header">
                    <div class="otp-user">
                        <i class="fas fa-user"></i>
                        <strong>${otp.username}</strong>
                    </div>
                    <div class="otp-status ${statusClass}">
                        <i class="fas ${statusIcon}"></i>
                        ${statusText}
                    </div>
                </div>

                <div class="otp-details">
                    <div class="otp-code">
                        <span class="label">OTP Kodu:</span>
                        <span class="code ${otp.status === 'active' ? 'active' : ''}">${otp.otp_code}</span>
                        ${otp.status === 'active' ? `<button class="copy-btn" onclick="copyToClipboard('${otp.otp_code}')" title="Kopyala"><i class="fas fa-copy"></i></button>` : ''}
                    </div>

                    <div class="otp-time">
                        <div class="time-info">
                            <span class="label">Oluşturulma:</span>
                            <span class="value">${createdDate.toLocaleString('tr-TR')}</span>
                        </div>
                        <div class="time-info">
                            <span class="label">Bitiş:</span>
                            <span class="value">${expiresDate.toLocaleString('tr-TR')}</span>
                        </div>
                        ${otp.status === 'active' ? `
                            <div class="time-info countdown">
                                <span class="label">Kalan Süre:</span>
                                <span class="value countdown-timer" data-expires="${otp.expires_at}">${timeLeftText}</span>
                            </div>
                        ` : ''}
                    </div>

                    <div class="otp-meta">
                        <div class="meta-info">
                            <span class="label">IP:</span>
                            <span class="value">${otp.ip_address}</span>
                        </div>
                    </div>
                </div>

                <div class="otp-actions">
                    ${otp.status === 'active' ? `
                        <button class="btn-warning" onclick="expireOTP('${otp.id}')" title="Süresi Dolmuş Olarak İşaretle">
                            <i class="fas fa-clock"></i>
                            Süresi Doldur
                        </button>
                    ` : ''}
                    <button class="btn-danger" onclick="deleteOTP('${otp.id}')" title="OTP'yi Sil">
                        <i class="fas fa-trash"></i>
                        Sil
                    </button>
                </div>
            </div>
        `;
    }).join('');

    otpListContainer.innerHTML = otpsHTML;

    // Geri sayımları başlat
    startOTPCountdowns();
}

// OTP istatistiklerini güncelle
function updateOTPStats(otps) {
    const activeCount = otps.filter(otp => otp.status === 'active').length;
    const usedCount = otps.filter(otp => otp.status === 'used').length;
    const expiredCount = otps.filter(otp => otp.status === 'expired').length;
    const totalCount = otps.length;

    document.getElementById('activeOTPCount').textContent = activeCount;
    document.getElementById('usedOTPCount').textContent = usedCount;
    document.getElementById('expiredOTPCount').textContent = expiredCount;
    document.getElementById('totalOTPCount').textContent = totalCount;
}

// OTP geri sayımlarını başlat
function startOTPCountdowns() {
    const countdownElements = document.querySelectorAll('.countdown-timer');

    countdownElements.forEach(element => {
        const expiresAt = new Date(element.dataset.expires);

        const updateCountdown = () => {
            const now = new Date();
            const timeLeft = Math.max(0, Math.floor((expiresAt - now) / 1000));

            if (timeLeft > 0) {
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                element.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
            } else {
                element.textContent = '00:00';
                element.parentElement.classList.add('expired');
                // Listeyi yenile
                setTimeout(() => loadOTPList(), 1000);
            }
        };

        updateCountdown();
        setInterval(updateCountdown, 1000);
    });
}

// OTP listesini filtrele
function filterOTPList() {
    const filter = document.getElementById('otpFilter').value;

    let filteredOTPs = otpList;

    if (filter !== 'all') {
        filteredOTPs = otpList.filter(otp => otp.status === filter);
    }

    displayOTPList(filteredOTPs);
}

// OTP listesini yenile
function refreshOTPList() {
    loadOTPList();
    showAlert('OTP listesi yenilendi!', 'success');
}

// OTP sil
async function deleteOTP(otpId) {
    if (!confirm('Bu OTP\'yi silmek istediğinizden emin misiniz?')) {
        return;
    }

    try {
        const response = await fetch('otp_api.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'delete_otp',
                otp_id: otpId
            })
        });

        const data = await response.json();

        if (data.success) {
            showAlert('OTP başarıyla silindi!', 'success');
            loadOTPList();
        } else {
            showAlert(data.message || 'OTP silinemedi!', 'error');
        }
    } catch (error) {
        console.error('Delete OTP error:', error);
        showAlert('OTP silinirken hata oluştu!', 'error');
    }
}

// Ürün sil
async function deleteExpiryItem(itemId) {
    if (!confirm('Bu ürünü silmek istediğinizden emin misiniz?')) {
        return;
    }

    try {
        const response = await fetch('expiry_api.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'delete_expiry',
                id: itemId
            })
        });

        const data = await response.json();

        if (data.success) {
            showAlert('Ürün başarıyla silindi!', 'success');
            loadExpiryData();
        } else {
            showAlert(data.message || 'Ürün silinemedi!', 'error');
        }
    } catch (error) {
        console.error('Delete expiry error:', error);
        showAlert('Ürün silinemedi!', 'error');
    }
}

// Filtreleme
function filterExpiryItems() {
    const filter = document.getElementById('expiryFilter').value;
    const today = new Date();

    let filteredItems = expiryItems;

    if (filter !== 'all') {
        filteredItems = expiryItems.filter(item => {
            const expiryDate = new Date(item.expiry_date);
            const diffDays = Math.ceil((expiryDate - today) / (1000 * 60 * 60 * 24));

            switch (filter) {
                case 'critical':
                    return diffDays >= 0 && diffDays <= 3;
                case 'warning':
                    return diffDays > 3 && diffDays <= 7;
                case 'normal':
                    return diffDays > 30;
                case 'expired':
                    return diffDays < 0;
                default:
                    return true;
            }
        });
    }

    displayExpiryItems(filteredItems);
}

// Sıralama
function sortExpiryItems() {
    const sortBy = document.getElementById('expirySortBy').value;
    let sortedItems = [...expiryItems];

    switch (sortBy) {
        case 'date_asc':
            sortedItems.sort((a, b) => new Date(a.expiry_date) - new Date(b.expiry_date));
            break;
        case 'date_desc':
            sortedItems.sort((a, b) => new Date(b.expiry_date) - new Date(a.expiry_date));
            break;
        case 'name_asc':
            sortedItems.sort((a, b) => a.product_name.localeCompare(b.product_name));
            break;
        case 'added_desc':
            sortedItems.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
            break;
    }

    displayExpiryItems(sortedItems);
}

// Session timeout uyarısı (isteğe bağlı)
let sessionWarningShown = false;
setInterval(function() {
    const loginTime = sessionStorage.getItem('loginTime');
    if (loginTime) {
        const elapsed = Date.now() - parseInt(loginTime);
        const sessionTimeout = 50 * 60 * 1000; // 50 dakika

        if (elapsed > sessionTimeout && !sessionWarningShown) {
            sessionWarningShown = true;
            showAlert('Oturumunuz yakında sona erecek. Lütfen sayfayı yenileyin.', 'warning');
        }
    }
}, 60000); // Her dakika kontrol et
