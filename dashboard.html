<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Aylix Portal</title>
    <link rel="stylesheet" href="dashboard.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-shield-alt"></i>
                    <span>Aylix Portal</span>
                </div>
            </div>
            
            <nav class="sidebar-nav">
                <ul>
                    <li class="nav-item active">
                        <a href="#" class="nav-link" data-section="search">
                            <i class="fas fa-search"></i>
                            <span>Ürün Arama</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" data-section="profile">
                            <i class="fas fa-user"></i>
                            <span>Profil</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" data-section="expiry">
                            <i class="fas fa-calendar-times"></i>
                            <span>Son Kullanım Tarihi</span>
                        </a>
                    </li>
                    <li class="nav-item admin-only">
                        <a href="#" class="nav-link" data-section="otp">
                            <i class="fas fa-key"></i>
                            <span>OTP Yönetimi</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="header">
                <div class="header-left">
                    <button class="sidebar-toggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1>Dashboard</h1>
                </div>
                
                <div class="header-right">
                    <div class="notifications">
                        <button class="notification-btn">
                            <i class="fas fa-bell"></i>
                            <span class="notification-badge">3</span>
                        </button>
                    </div>
                    
                    <div class="user-menu">
                        <button class="user-btn" onclick="toggleUserMenu()">
                            <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face&auto=format" alt="User" class="user-avatar" id="headerUserAvatar">
                            <span class="user-name" id="userName">Kullanıcı</span>
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        
                        <div class="user-dropdown" id="userDropdown">
                            <a href="#" class="dropdown-item" onclick="showSection('profile'); toggleUserMenu();">
                                <i class="fas fa-user"></i>
                                Profil
                            </a>
                            <a href="admin.html" class="dropdown-item" id="adminPanelLink" style="display: none;">
                                <i class="fas fa-tools"></i>
                                Admin Panel
                            </a>
                            <div class="dropdown-divider"></div>
                            <a href="#" class="dropdown-item" onclick="logout()">
                                <i class="fas fa-sign-out-alt"></i>
                                Çıkış Yap
                            </a>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Product Search Section -->
            <div id="search-section" class="content-section active">
                <div class="dashboard-content">
                    <!-- Search Header -->
                    <div class="search-header">
                        <h2>Ürün Arama</h2>
                        <p>Barkod veya ürün adı ile arama yapabilirsiniz</p>
                    </div>

                    <!-- Search Form -->
                    <div class="search-form-container">
                        <div class="search-form">
                            <div class="search-input-group">
                                <input type="text" id="productSearch" placeholder="Barkod veya ürün adı girin..." class="search-input">
                                <button type="button" id="searchBtn" class="search-btn">
                                    <i class="fas fa-search"></i>
                                    Ara
                                </button>
                            </div>
                            <div class="search-options">
                                <label class="search-option">
                                    <input type="radio" name="searchType" value="barcode" checked>
                                    <span>Barkod ile ara</span>
                                </label>
                                <label class="search-option">
                                    <input type="radio" name="searchType" value="name">
                                    <span>Ürün adı ile ara</span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Search Results -->
                    <div id="searchResults" class="search-results" style="display: none;">
                        <div class="results-header">
                            <h3>Arama Sonuçları</h3>
                            <span id="resultsCount" class="results-count"></span>
                        </div>
                        <div id="productList" class="product-list">
                            <!-- Ürünler JavaScript ile doldurulacak -->
                        </div>
                    </div>

                    <!-- Product Detail Modal -->
                    <div id="productModal" class="modal">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h3>Ürün Detayları</h3>
                                <button class="modal-close" onclick="closeProductModal()">&times;</button>
                            </div>
                            <div class="modal-body" id="productDetails">
                                <!-- Ürün detayları JavaScript ile doldurulacak -->
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn-secondary" onclick="closeProductModal()">Kapat</button>
                                <button type="button" id="favoriteBtn" class="btn-primary" onclick="toggleFavorite()">
                                    <i class="fas fa-heart"></i>
                                    Favorilere Ekle
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Favorites Section -->
                    <div class="favorites-section">
                        <div class="section-header">
                            <h3>Favori Ürünlerim</h3>
                            <button class="btn-secondary" onclick="loadFavorites()">
                                <i class="fas fa-sync"></i>
                                Yenile
                            </button>
                        </div>
                        <div id="favoritesList" class="favorites-list">
                            <!-- Favori ürünler JavaScript ile doldurulacak -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Profile Section -->
            <div id="profile-section" class="content-section">
                <div class="dashboard-content">
                    <div class="profile-container">
                        <!-- Profile Header -->
                        <div class="profile-header">
                            <div class="profile-cover">
                                <div class="profile-avatar-container">
                                    <div class="profile-avatar">
                                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=120&h=120&fit=crop&crop=face&auto=format" alt="Profil Resmi" id="profileAvatar">
                                        <button class="avatar-edit-btn" onclick="changeProfilePicture()">
                                            <i class="fas fa-camera"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="profile-info">
                                    <h1 class="profile-name" id="profileName">Kullanıcı Adı</h1>
                                    <p class="profile-email" id="profileEmail"><EMAIL></p>
                                    <div class="profile-stats">
                                        <div class="stat-item">
                                            <span class="stat-number" id="favoriteCount">0</span>
                                            <span class="stat-label">Favori Ürün</span>
                                        </div>
                                        <div class="stat-item">
                                            <span class="stat-number" id="searchCount">0</span>
                                            <span class="stat-label">Arama</span>
                                        </div>
                                        <div class="stat-item">
                                            <span class="stat-number" id="memberDays">0</span>
                                            <span class="stat-label">Gün Üye</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Profile Content -->
                        <div class="profile-content">
                            <div class="profile-tabs">
                                <button class="tab-btn active" onclick="showProfileTab('info')">
                                    <i class="fas fa-info-circle"></i>
                                    Kişisel Bilgiler
                                </button>
                                <button class="tab-btn" onclick="showProfileTab('notifications')">
                                    <i class="fas fa-bell"></i>
                                    Bildirimler
                                </button>
                                <button class="tab-btn" onclick="showProfileTab('security')">
                                    <i class="fas fa-shield-alt"></i>
                                    Güvenlik
                                </button>
                                <button class="tab-btn" onclick="showProfileTab('activity')">
                                    <i class="fas fa-history"></i>
                                    Aktivite
                                </button>
                            </div>

                            <!-- Personal Info Tab -->
                            <div id="info-tab" class="tab-content active">
                                <div class="profile-form">
                                    <h3>Kişisel Bilgiler</h3>
                                    <div class="form-grid">
                                        <div class="form-group">
                                            <label>Ad</label>
                                            <input type="text" id="profileFirstName" placeholder="Adınız">
                                        </div>
                                        <div class="form-group">
                                            <label>Soyad</label>
                                            <input type="text" id="profileLastName" placeholder="Soyadınız">
                                        </div>
                                        <div class="form-group">
                                            <label>E-posta</label>
                                            <input type="email" id="profileEmailInput" placeholder="E-posta adresiniz" readonly>
                                        </div>
                                        <div class="form-group">
                                            <label>Telefon</label>
                                            <input type="tel" id="profilePhone" placeholder="Telefon numaranız">
                                        </div>
                                        <div class="form-group full-width">
                                            <label>Hakkımda</label>
                                            <textarea id="profileBio" placeholder="Kendiniz hakkında kısa bilgi..." rows="3"></textarea>
                                        </div>
                                    </div>
                                    <div class="form-actions">
                                        <button type="button" class="btn-secondary" onclick="resetProfileForm()">
                                            <i class="fas fa-undo"></i>
                                            Sıfırla
                                        </button>
                                        <button type="button" class="btn-primary" onclick="saveProfileInfo()">
                                            <i class="fas fa-save"></i>
                                            Kaydet
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Notifications Tab -->
                            <div id="notifications-tab" class="tab-content">
                                <div class="profile-form">
                                    <h3>Bildirim Ayarları</h3>
                                    <div class="notification-settings">
                                        <div class="notification-group">
                                            <h4>E-posta Bildirimleri</h4>
                                            <div class="notification-item">
                                                <label class="notification-toggle">
                                                    <input type="checkbox" id="emailNotifications" checked>
                                                    <span class="toggle-slider"></span>
                                                    <span class="toggle-label">Genel e-posta bildirimleri</span>
                                                </label>
                                                <p class="notification-desc">Sistem güncellemeleri ve önemli duyurular</p>
                                            </div>
                                            <div class="notification-item">
                                                <label class="notification-toggle">
                                                    <input type="checkbox" id="expiryEmailNotifications" checked>
                                                    <span class="toggle-slider"></span>
                                                    <span class="toggle-label">Son kullanım tarihi uyarıları</span>
                                                </label>
                                                <p class="notification-desc">Ürünlerin son kullanım tarihi yaklaştığında</p>
                                            </div>
                                        </div>

                                        <div class="notification-group">
                                            <h4>Push Bildirimleri</h4>
                                            <div class="notification-item">
                                                <label class="notification-toggle">
                                                    <input type="checkbox" id="pushNotifications" checked>
                                                    <span class="toggle-slider"></span>
                                                    <span class="toggle-label">Tarayıcı bildirimleri</span>
                                                </label>
                                                <p class="notification-desc">Tarayıcı üzerinden anlık bildirimler</p>
                                            </div>
                                            <div class="notification-item">
                                                <label class="notification-toggle">
                                                    <input type="checkbox" id="criticalPushNotifications" checked>
                                                    <span class="toggle-slider"></span>
                                                    <span class="toggle-label">Kritik uyarılar</span>
                                                </label>
                                                <p class="notification-desc">Acil durumlar için önemli bildirimler</p>
                                            </div>
                                        </div>

                                        <div class="notification-group">
                                            <h4>SMS Bildirimleri</h4>
                                            <div class="notification-item">
                                                <label class="notification-toggle">
                                                    <input type="checkbox" id="smsNotifications">
                                                    <span class="toggle-slider"></span>
                                                    <span class="toggle-label">SMS bildirimleri</span>
                                                </label>
                                                <p class="notification-desc">Telefon numaranıza SMS gönderimi (yakında)</p>
                                            </div>
                                        </div>

                                        <div class="notification-group">
                                            <h4>Bildirim Zamanlaması</h4>
                                            <div class="notification-timing">
                                                <div class="timing-item">
                                                    <label>Kritik uyarı (gün önce)</label>
                                                    <select id="criticalDays">
                                                        <option value="1">1 gün</option>
                                                        <option value="2">2 gün</option>
                                                        <option value="3" selected>3 gün</option>
                                                        <option value="5">5 gün</option>
                                                    </select>
                                                </div>
                                                <div class="timing-item">
                                                    <label>Uyarı bildirimi (gün önce)</label>
                                                    <select id="warningDays">
                                                        <option value="5">5 gün</option>
                                                        <option value="7" selected>7 gün</option>
                                                        <option value="10">10 gün</option>
                                                        <option value="14">14 gün</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-actions">
                                        <button type="button" class="btn-secondary" onclick="resetNotificationSettings()">
                                            <i class="fas fa-undo"></i>
                                            Sıfırla
                                        </button>
                                        <button type="button" class="btn-primary" onclick="saveNotificationSettings()">
                                            <i class="fas fa-save"></i>
                                            Kaydet
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Security Tab -->
                            <div id="security-tab" class="tab-content">
                                <div class="profile-form">
                                    <h3>Güvenlik Ayarları</h3>
                                    <div class="security-section">
                                        <div class="security-item">
                                            <div class="security-info">
                                                <h4>Şifre Değiştir</h4>
                                                <p>Hesabınızın güvenliği için düzenli olarak şifrenizi değiştirin</p>
                                            </div>
                                            <button class="btn-secondary" onclick="showPasswordChangeForm()">
                                                <i class="fas fa-key"></i>
                                                Şifre Değiştir
                                            </button>
                                        </div>
                                        <div class="security-item">
                                            <div class="security-info">
                                                <h4>İki Faktörlü Doğrulama</h4>
                                                <p>Hesabınıza ek güvenlik katmanı ekleyin</p>
                                            </div>
                                            <button class="btn-secondary" disabled>
                                                <i class="fas fa-mobile-alt"></i>
                                                Yakında
                                            </button>
                                        </div>
                                        <div class="security-item">
                                            <div class="security-info">
                                                <h4>Oturum Geçmişi</h4>
                                                <p>Son giriş yapılan cihazları görüntüleyin</p>
                                            </div>
                                            <button class="btn-secondary" onclick="showLoginHistory()">
                                                <i class="fas fa-history"></i>
                                                Geçmişi Gör
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Password Change Form (Hidden) -->
                                    <div id="passwordChangeForm" class="password-form" style="display: none;">
                                        <h4>Şifre Değiştir</h4>
                                        <div class="form-group">
                                            <label>Mevcut Şifre</label>
                                            <input type="password" id="currentPasswordProfile" placeholder="Mevcut şifreniz">
                                        </div>
                                        <div class="form-group">
                                            <label>Yeni Şifre</label>
                                            <input type="password" id="newPasswordProfile" placeholder="Yeni şifreniz">
                                        </div>
                                        <div class="form-group">
                                            <label>Yeni Şifre Tekrar</label>
                                            <input type="password" id="confirmPasswordProfile" placeholder="Yeni şifrenizi tekrar girin">
                                        </div>
                                        <div class="form-actions">
                                            <button type="button" class="btn-secondary" onclick="hidePasswordChangeForm()">
                                                İptal
                                            </button>
                                            <button type="button" class="btn-primary" onclick="changePasswordProfile()">
                                                <i class="fas fa-key"></i>
                                                Şifre Değiştir
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Activity Tab -->
                            <div id="activity-tab" class="tab-content">
                                <div class="profile-form">
                                    <h3>Son Aktiviteler</h3>
                                    <div id="activityList" class="activity-list">
                                        <!-- Aktiviteler JavaScript ile doldurulacak -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Expiry Section -->
            <div id="expiry-section" class="content-section">
                <div class="dashboard-content">
                    <!-- Expiry Header -->
                    <div class="expiry-header">
                        <h2>Son Kullanım Tarihi Takibi</h2>
                        <p>Ürünlerinizin son kullanım tarihlerini takip edin ve zamanında uyarı alın</p>
                    </div>

                    <!-- Add Expiry Form -->
                    <div class="expiry-form-container">
                        <div class="expiry-form">
                            <h3>Yeni Ürün Ekle</h3>
                            <div class="expiry-input-group">
                                <div class="input-row">
                                    <div class="input-with-search">
                                        <input type="text" id="expiryProductCode" placeholder="Ürün kodunu girin..." class="expiry-input">
                                        <button type="button" id="searchProductBtn" class="search-product-btn">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                    <input type="text" id="expiryProductName" placeholder="Ürün adı (otomatik doldurulacak)" class="expiry-input" readonly>
                                </div>

                                <div class="input-row">
                                    <input type="date" id="expiryDate" class="expiry-input" required>
                                    <input type="number" id="expiryQuantity" placeholder="Adet" class="expiry-input" min="1" value="1">
                                </div>

                                <div class="input-row full-width">
                                    <div class="image-input-group">
                                        <input type="url" id="expiryImageUrl" placeholder="Ürün resmi URL'si (isteğe bağlı)" class="expiry-input image-url-input" onchange="previewExpiryImage()">
                                        <button type="button" class="preview-btn" onclick="previewExpiryImage()" title="Resmi Önizle">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="image-preview" id="expiryImagePreview" style="display: none;">
                                    <img id="expiryPreviewImg" src="" alt="Ürün Önizleme">
                                    <div class="image-overlay">
                                        <button type="button" class="remove-image-btn" onclick="removeExpiryImage()" title="Resmi Kaldır">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="input-row full-width">
                                    <textarea id="expiryNotes" placeholder="Ürün hakkında notlar (isteğe bağlı)" class="expiry-textarea" rows="2"></textarea>
                                </div>

                                <div class="form-actions">
                                    <button type="button" class="expiry-btn secondary" onclick="clearExpiryForm()">
                                        <i class="fas fa-eraser"></i>
                                        Temizle
                                    </button>
                                    <button type="button" id="addExpiryBtn" class="expiry-btn primary">
                                        <i class="fas fa-plus"></i>
                                        Ürün Ekle
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Filter Options -->
                    <div class="expiry-filters">
                        <div class="filter-group">
                            <label>Filtrele:</label>
                            <select id="expiryFilter" class="filter-select">
                                <option value="all">Tüm Ürünler</option>
                                <option value="critical">Kritik (3 gün)</option>
                                <option value="warning">Uyarı (7 gün)</option>
                                <option value="normal">Normal (30+ gün)</option>
                                <option value="expired">Süresi Geçmiş</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label>Sırala:</label>
                            <select id="expirySortBy" class="filter-select">
                                <option value="date_asc">Tarihe Göre (Yakın)</option>
                                <option value="date_desc">Tarihe Göre (Uzak)</option>
                                <option value="name_asc">İsme Göre (A-Z)</option>
                                <option value="added_desc">Eklenme Tarihi</option>
                            </select>
                        </div>
                    </div>

                    <!-- Expiry List -->
                    <div id="expiryResults" class="expiry-results">
                        <div class="results-header">
                            <h3>Takip Edilen Ürünler</h3>
                            <span id="expiryCount" class="results-count">0 ürün</span>
                        </div>
                        <div id="expiryList" class="expiry-list">
                            <!-- Ürünler JavaScript ile doldurulacak -->
                        </div>
                    </div>

                    <!-- Statistics Cards -->
                    <div class="expiry-stats">
                        <div class="stat-card critical">
                            <div class="stat-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="stat-content">
                                <h3 id="criticalCount">0</h3>
                                <p>Kritik Ürün</p>
                                <span class="stat-desc">3 gün içinde</span>
                            </div>
                        </div>

                        <div class="stat-card warning">
                            <div class="stat-icon">
                                <i class="fas fa-exclamation-circle"></i>
                            </div>
                            <div class="stat-content">
                                <h3 id="warningCount">0</h3>
                                <p>Uyarı Ürünü</p>
                                <span class="stat-desc">7 gün içinde</span>
                            </div>
                        </div>

                        <div class="stat-card expired">
                            <div class="stat-icon">
                                <i class="fas fa-times-circle"></i>
                            </div>
                            <div class="stat-content">
                                <h3 id="expiredCount">0</h3>
                                <p>Süresi Geçmiş</p>
                                <span class="stat-desc">İmha edilmeli</span>
                            </div>
                        </div>

                        <div class="stat-card total">
                            <div class="stat-icon">
                                <i class="fas fa-boxes"></i>
                            </div>
                            <div class="stat-content">
                                <h3 id="totalCount">0</h3>
                                <p>Toplam Ürün</p>
                                <span class="stat-desc">Takip edilen</span>
                            </div>
                        </div>
                    </div>

                    <!-- Share Modal -->
                    <div id="shareModal" class="modal">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h3>Ürün Paylaş</h3>
                                <button class="modal-close" onclick="closeShareModal()">&times;</button>
                            </div>
                            <div class="modal-body" id="shareContent">
                                <!-- Paylaşım içeriği JavaScript ile doldurulacak -->
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn-secondary" onclick="closeShareModal()">Kapat</button>
                                <button type="button" class="btn-primary" onclick="copyShareContent()">
                                    <i class="fas fa-copy"></i>
                                    Kopyala
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


            <!-- OTP Management Section -->
            <div id="otp-section" class="content-section">
                <div class="dashboard-content">
                    <div class="section-header">
                        <h2><i class="fas fa-key"></i> OTP Yönetimi</h2>
                        <p>Şifre sıfırlama kodlarını yönetin</p>
                        <button class="btn-primary" onclick="refreshOTPList()">
                            <i class="fas fa-sync-alt"></i>
                            Yenile
                        </button>
                    </div>

                    <!-- OTP İstatistikleri -->
                    <div class="otp-stats">
                        <div class="stat-card active">
                            <div class="stat-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="activeOTPCount">0</h3>
                                <p>Aktif OTP</p>
                            </div>
                        </div>
                        <div class="stat-card used">
                            <div class="stat-icon">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="usedOTPCount">0</h3>
                                <p>Kullanılmış</p>
                            </div>
                        </div>
                        <div class="stat-card expired">
                            <div class="stat-icon">
                                <i class="fas fa-times"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="expiredOTPCount">0</h3>
                                <p>Süresi Dolmuş</p>
                            </div>
                        </div>
                        <div class="stat-card total">
                            <div class="stat-icon">
                                <i class="fas fa-list"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="totalOTPCount">0</h3>
                                <p>Toplam</p>
                            </div>
                        </div>
                    </div>

                    <!-- OTP Listesi -->
                    <div class="otp-container">
                        <div class="otp-header">
                            <h3>OTP Kodları</h3>
                            <div class="otp-filters">
                                <select id="otpFilter" onchange="filterOTPList()">
                                    <option value="all">Tümü</option>
                                    <option value="active">Aktif</option>
                                    <option value="used">Kullanılmış</option>
                                    <option value="expired">Süresi Dolmuş</option>
                                </select>
                            </div>
                        </div>

                        <div id="otpList" class="otp-list">
                            <!-- OTP'ler JavaScript ile doldurulacak -->
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Alert Container -->
    <div id="alertContainer"></div>

    <script src="dashboard.js"></script>
</body>
</html>
