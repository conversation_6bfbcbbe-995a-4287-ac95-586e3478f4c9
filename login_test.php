<?php
require_once 'config.php';

if ($_POST) {
    $username = $_POST['username'];
    $password = $_POST['password'];
    
    echo "<h2>Login Test Sonuçları:</h2>";
    echo "<p><strong><PERSON><PERSON><PERSON><PERSON><PERSON> Adı:</strong> " . htmlspecialchars($username) . "</p>";
    echo "<p><strong>Şifre:</strong> " . htmlspecialchars($password) . "</p>";
    
    try {
        // Kullanıcıyı veritabanından bul
        $stmt = $pdo->prepare("
            SELECT id, username, password_hash, email, is_active, last_login, 
                   failed_login_attempts, locked_until
            FROM users 
            WHERE username = ? OR email = ?
        ");
        $stmt->execute([$username, $username]);
        $user = $stmt->fetch();
        
        if (!$user) {
            echo "<p style='color: red;'><strong>Sonuç:</strong> <PERSON><PERSON><PERSON><PERSON><PERSON> bulunamadı!</p>";
        } else {
            echo "<p style='color: green;'><strong>Kullanıcı Bulundu:</strong></p>";
            echo "<ul>";
            echo "<li>ID: " . $user['id'] . "</li>";
            echo "<li>Username: " . $user['username'] . "</li>";
            echo "<li>Email: " . $user['email'] . "</li>";
            echo "<li>Active: " . ($user['is_active'] ? 'Evet' : 'Hayır') . "</li>";
            echo "<li>Hash: " . substr($user['password_hash'], 0, 30) . "...</li>";
            echo "</ul>";
            
            // Şifre kontrolü
            $passwordValid = password_verify($password, $user['password_hash']);
            echo "<p><strong>Şifre Kontrolü:</strong> " . ($passwordValid ? '<span style="color: green;">BAŞARILI</span>' : '<span style="color: red;">BAŞARISIZ</span>') . "</p>";
            
            if ($passwordValid) {
                echo "<p style='color: green; font-weight: bold;'>✓ LOGIN BAŞARILI!</p>";
            } else {
                echo "<p style='color: red; font-weight: bold;'>✗ LOGIN BAŞARISIZ!</p>";
                
                // Hash test
                echo "<h3>Hash Test:</h3>";
                $testHash = password_hash($password, PASSWORD_DEFAULT);
                echo "<p>Yeni hash: " . $testHash . "</p>";
                echo "<p>Yeni hash test: " . (password_verify($password, $testHash) ? 'BAŞARILI' : 'BAŞARISIZ') . "</p>";
            }
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'><strong>Hata:</strong> " . $e->getMessage() . "</p>";
    }
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Login Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        form { background: #f5f5f5; padding: 20px; border-radius: 8px; max-width: 400px; }
        input { width: 100%; padding: 10px; margin: 5px 0 15px 0; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007cba; color: white; padding: 12px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #005a87; }
    </style>
</head>
<body>
    <h1>Login Test</h1>
    
    <form method="post">
        <p>
            <label>Kullanıcı Adı:</label><br>
            <input type="text" name="username" value="admin" required>
        </p>
        <p>
            <label>Şifre:</label><br>
            <input type="text" name="password" value="123456" required>
        </p>
        <p>
            <button type="submit">Test Et</button>
        </p>
    </form>
    
    <hr>
    <h3>Demo Hesaplar:</h3>
    <ul>
        <li><strong>Admin:</strong> admin / 123456</li>
        <li><strong>Demo:</strong> demo / 123456</li>
    </ul>
</body>
</html>
