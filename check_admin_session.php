<?php
require_once 'config.php';

// Güvenli session başlat
startSecureSession();

// JSON header'ı ayarla
header('Content-Type: application/json; charset=utf-8');
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');

try {
    // Session kontrolü
    if (!isset($_SESSION['user_id']) || !isset($_SESSION['username'])) {
        sendJSONResponse([
            'authenticated' => false,
            'message' => 'Oturum bulunamadı'
        ]);
    }
    
    // Session timeout kontrolü
    $sessionTimeout = SESSION_TIMEOUT;
    if (isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity']) > $sessionTimeout) {
        session_destroy();
        
        sendJSONResponse([
            'authenticated' => false,
            'message' => 'Oturum süresi doldu'
        ]);
    }
    
    // Son aktivite zamanını güncelle
    $_SESSION['last_activity'] = time();
    
    // Kullanıcı bilgilerini veritabanından al ve admin kontrolü yap
    $stmt = $pdo->prepare("
        SELECT id, username, email, first_name, last_name, role, last_login, is_active
        FROM users 
        WHERE id = ? AND is_active = 1
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $user = $stmt->fetch();
    
    if (!$user) {
        session_destroy();
        
        sendJSONResponse([
            'authenticated' => false,
            'message' => 'Kullanıcı bulunamadı'
        ]);
    }
    
    // Admin yetkisi kontrolü
    if ($user['role'] !== 'admin') {
        sendJSONResponse([
            'authenticated' => false,
            'message' => 'Admin yetkisi gerekli'
        ], 403);
    }
    
    // Session hijacking koruması
    $currentIP = getClientIP();
    if (isset($_SESSION['ip_address']) && $_SESSION['ip_address'] !== $currentIP) {
        session_destroy();
        
        logError('Possible admin session hijacking detected', [
            'user_id' => $_SESSION['user_id'],
            'original_ip' => $_SESSION['ip_address'],
            'current_ip' => $currentIP
        ]);
        
        sendJSONResponse([
            'authenticated' => false,
            'message' => 'Güvenlik ihlali tespit edildi'
        ]);
    }
    
    // IP adresini session'a kaydet (ilk kez)
    if (!isset($_SESSION['ip_address'])) {
        $_SESSION['ip_address'] = $currentIP;
    }
    
    // Başarılı response
    sendJSONResponse([
        'authenticated' => true,
        'user' => [
            'id' => $user['id'],
            'username' => $user['username'],
            'email' => $user['email'],
            'first_name' => $user['first_name'],
            'last_name' => $user['last_name'],
            'role' => $user['role'],
            'last_login' => $user['last_login']
        ],
        'session_info' => [
            'login_time' => $_SESSION['login_time'] ?? time(),
            'last_activity' => $_SESSION['last_activity'],
            'expires_in' => $sessionTimeout - (time() - $_SESSION['last_activity'])
        ]
    ]);
    
} catch (PDOException $e) {
    logError('Admin session check database error: ' . $e->getMessage());
    
    sendJSONResponse([
        'authenticated' => false,
        'message' => 'Veritabanı hatası'
    ], 500);
    
} catch (Exception $e) {
    logError('Admin session check general error: ' . $e->getMessage());
    
    sendJSONResponse([
        'authenticated' => false,
        'message' => 'Sistem hatası'
    ], 500);
}
?>
